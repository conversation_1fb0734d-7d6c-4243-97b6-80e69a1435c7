-- إجراءات مخزنة لنظام الخصم الجديد (مبلغ ثابت) - النسخة النهائية
-- Stored Procedures for New Discount System (Fixed Amount) - Final Version

USE INV_DB
GO

-- إجراء لعرض تفاصيل فاتورة مع الخصومات (مصحح)
-- Procedure to display invoice details with discounts (corrected)
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetInvoiceDetails')
    DROP PROCEDURE sp_GetInvoiceDetails
GO

CREATE PROCEDURE sp_GetInvoiceDetails
    @InvoiceID INT
AS
BEGIN
    SELECT 
        ip.IPo_ID,
        ISNULL(p.ProductName, 'غير محدد') as ProductName,
        ip.SellingPrice as 'سعر_البيع',
        ip.Qty as 'الكمية',
        ip.Amount as 'المبلغ_الأساسي',
        ip.Discount as 'خصم_المبلغ',
        ip.VAT as 'ضريبة_القيمة_المضافة',
        ip.TotalAmount as 'المجموع_الإجمالي'
    FROM Invoice_Product ip
    LEFT JOIN Product p ON ip.ProductID = p.PID
    WHERE ip.InvoiceID = @InvoiceID
    ORDER BY ip.IPo_ID
END
GO

-- إجراء لاختبار النظام الجديد
-- Procedure to test the new system
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_TestDiscountSystem')
    DROP PROCEDURE sp_TestDiscountSystem
GO

CREATE PROCEDURE sp_TestDiscountSystem
AS
BEGIN
    PRINT 'اختبار نظام الخصم الجديد:'
    PRINT 'Testing new discount system:'
    
    -- عرض البيانات الحالية
    SELECT 
        'البيانات الحالية' as Status,
        IPo_ID,
        SellingPrice,
        Qty,
        Amount,
        DiscountPer as 'خصم_نسبة_مئوية',
        Discount as 'خصم_مبلغ',
        TotalAmount
    FROM Invoice_Product
    
    PRINT 'تم الاختبار بنجاح!'
END
GO

PRINT 'تم إنشاء الإجراءات المخزنة المصححة بنجاح!'
PRINT 'Corrected stored procedures created successfully!'
