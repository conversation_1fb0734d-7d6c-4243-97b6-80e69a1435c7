using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Data.SqlClient;

namespace SimpleInvoiceForm
{
    public partial class Form1 : Form
    {
        private string connectionString = "Data Source=.\\SQLEXPRESS;Initial Catalog=INV_DB;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True";
        
        public Form1()
        {
            InitializeComponent();
        }
        
        private void InitializeComponent()
        {
            this.Text = "🎯 فاتورة المبيعات - خصم المبلغ الثابت";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.Font = new Font("Tahoma", 12F);
            this.BackColor = Color.LightBlue;
            
            // Title
            Label lblTitle = new Label();
            lblTitle.Text = "🎯 فاتورة المبيعات مع خصم المبلغ الثابت";
            lblTitle.Font = new Font("Tahoma", 18F, FontStyle.Bold);
            lblTitle.ForeColor = Color.DarkBlue;
            lblTitle.AutoSize = true;
            lblTitle.Location = new Point(200, 20);
            this.Controls.Add(lblTitle);
            
            // Product Name
            Label lblProduct = new Label();
            lblProduct.Text = "اسم المنتج:";
            lblProduct.Location = new Point(650, 80);
            lblProduct.AutoSize = true;
            this.Controls.Add(lblProduct);
            
            TextBox txtProduct = new TextBox();
            txtProduct.Name = "txtProduct";
            txtProduct.Location = new Point(450, 77);
            txtProduct.Size = new Size(180, 30);
            txtProduct.Font = new Font("Tahoma", 12F);
            this.Controls.Add(txtProduct);
            
            // Selling Price
            Label lblPrice = new Label();
            lblPrice.Text = "سعر البيع:";
            lblPrice.Location = new Point(350, 80);
            lblPrice.AutoSize = true;
            this.Controls.Add(lblPrice);
            
            TextBox txtPrice = new TextBox();
            txtPrice.Name = "txtPrice";
            txtPrice.Location = new Point(200, 77);
            txtPrice.Size = new Size(120, 30);
            txtPrice.Font = new Font("Tahoma", 12F);
            this.Controls.Add(txtPrice);
            
            // Quantity
            Label lblQty = new Label();
            lblQty.Text = "الكمية:";
            lblQty.Location = new Point(650, 130);
            lblQty.AutoSize = true;
            this.Controls.Add(lblQty);
            
            NumericUpDown nudQty = new NumericUpDown();
            nudQty.Name = "nudQty";
            nudQty.Location = new Point(550, 127);
            nudQty.Size = new Size(80, 30);
            nudQty.Font = new Font("Tahoma", 12F);
            nudQty.Minimum = 1;
            nudQty.Value = 1;
            this.Controls.Add(nudQty);
            
            // *** DISCOUNT AMOUNT - الميزة الجديدة ***
            Label lblDiscount = new Label();
            lblDiscount.Text = "🎯 خصم المبلغ (ريال):";
            lblDiscount.Location = new Point(400, 130);
            lblDiscount.AutoSize = true;
            lblDiscount.ForeColor = Color.Red;
            lblDiscount.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.Controls.Add(lblDiscount);
            
            TextBox txtDiscount = new TextBox();
            txtDiscount.Name = "txtDiscount";
            txtDiscount.Location = new Point(200, 127);
            txtDiscount.Size = new Size(120, 30);
            txtDiscount.Font = new Font("Tahoma", 12F);
            txtDiscount.Text = "0";
            txtDiscount.BackColor = Color.LightYellow;
            txtDiscount.ForeColor = Color.Red;
            this.Controls.Add(txtDiscount);
            
            // VAT
            Label lblVAT = new Label();
            lblVAT.Text = "ضريبة القيمة المضافة (%):";
            lblVAT.Location = new Point(500, 180);
            lblVAT.AutoSize = true;
            this.Controls.Add(lblVAT);
            
            TextBox txtVAT = new TextBox();
            txtVAT.Name = "txtVAT";
            txtVAT.Location = new Point(350, 177);
            txtVAT.Size = new Size(120, 30);
            txtVAT.Font = new Font("Tahoma", 12F);
            txtVAT.Text = "15";
            this.Controls.Add(txtVAT);
            
            // Calculation Display
            Label lblCalcTitle = new Label();
            lblCalcTitle.Text = "🧮 الحسابات التلقائية:";
            lblCalcTitle.Location = new Point(600, 230);
            lblCalcTitle.AutoSize = true;
            lblCalcTitle.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            lblCalcTitle.ForeColor = Color.DarkGreen;
            this.Controls.Add(lblCalcTitle);
            
            Label lblCalcResult = new Label();
            lblCalcResult.Name = "lblCalcResult";
            lblCalcResult.Text = "أدخل البيانات لرؤية الحسابات";
            lblCalcResult.Location = new Point(50, 270);
            lblCalcResult.Size = new Size(700, 100);
            lblCalcResult.Font = new Font("Tahoma", 11F);
            lblCalcResult.ForeColor = Color.DarkBlue;
            this.Controls.Add(lblCalcResult);
            
            // Add Product Button
            Button btnAdd = new Button();
            btnAdd.Text = "➕ إضافة المنتج مع الخصم";
            btnAdd.Location = new Point(450, 400);
            btnAdd.Size = new Size(250, 50);
            btnAdd.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            btnAdd.BackColor = Color.LightGreen;
            btnAdd.ForeColor = Color.DarkGreen;
            btnAdd.Click += BtnAdd_Click;
            this.Controls.Add(btnAdd);
            
            // Calculate Button
            Button btnCalc = new Button();
            btnCalc.Text = "🧮 حساب الإجمالي";
            btnCalc.Location = new Point(200, 400);
            btnCalc.Size = new Size(200, 50);
            btnCalc.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            btnCalc.BackColor = Color.LightCoral;
            btnCalc.ForeColor = Color.DarkRed;
            btnCalc.Click += BtnCalc_Click;
            this.Controls.Add(btnCalc);
            
            // Show Current Invoice Button
            Button btnShow = new Button();
            btnShow.Text = "📊 عرض الفاتورة الحالية";
            btnShow.Location = new Point(50, 400);
            btnShow.Size = new Size(120, 50);
            btnShow.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnShow.BackColor = Color.LightBlue;
            btnShow.Click += BtnShow_Click;
            this.Controls.Add(btnShow);
            
            // Status Label
            Label lblStatus = new Label();
            lblStatus.Name = "lblStatus";
            lblStatus.Text = "✅ جاهز لإدخال البيانات";
            lblStatus.Location = new Point(50, 500);
            lblStatus.Size = new Size(700, 30);
            lblStatus.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            lblStatus.ForeColor = Color.Green;
            this.Controls.Add(lblStatus);
            
            // Add event handlers for real-time calculation
            txtPrice.TextChanged += UpdateCalculation;
            nudQty.ValueChanged += UpdateCalculation;
            txtDiscount.TextChanged += UpdateCalculation;
            txtVAT.TextChanged += UpdateCalculation;
        }
        
        private void UpdateCalculation(object sender, EventArgs e)
        {
            try
            {
                var txtPrice = this.Controls["txtPrice"] as TextBox;
                var nudQty = this.Controls["nudQty"] as NumericUpDown;
                var txtDiscount = this.Controls["txtDiscount"] as TextBox;
                var txtVAT = this.Controls["txtVAT"] as TextBox;
                var lblCalcResult = this.Controls["lblCalcResult"] as Label;
                
                decimal price = decimal.TryParse(txtPrice.Text, out decimal p) ? p : 0;
                int qty = (int)nudQty.Value;
                decimal discount = decimal.TryParse(txtDiscount.Text, out decimal d) ? d : 0;
                decimal vatPercent = decimal.TryParse(txtVAT.Text, out decimal v) ? v : 15;
                
                decimal amount = price * qty;
                decimal amountAfterDiscount = amount - discount;
                decimal vat = amountAfterDiscount * vatPercent / 100;
                decimal total = amountAfterDiscount + vat;
                
                lblCalcResult.Text = $@"
🧮 الحسابات التفصيلية:
   المبلغ الأساسي = {price} × {qty} = {amount:F2} ريال
   🎯 خصم المبلغ = {discount:F2} ريال
   المبلغ بعد الخصم = {amount:F2} - {discount:F2} = {amountAfterDiscount:F2} ريال
   ضريبة القيمة المضافة = {amountAfterDiscount:F2} × {vatPercent}% = {vat:F2} ريال
   💰 المجموع الإجمالي = {amountAfterDiscount:F2} + {vat:F2} = {total:F2} ريال";
            }
            catch
            {
                // Ignore errors during typing
            }
        }
        
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var txtProduct = this.Controls["txtProduct"] as TextBox;
                var txtPrice = this.Controls["txtPrice"] as TextBox;
                var nudQty = this.Controls["nudQty"] as NumericUpDown;
                var txtDiscount = this.Controls["txtDiscount"] as TextBox;
                var txtVAT = this.Controls["txtVAT"] as TextBox;
                var lblStatus = this.Controls["lblStatus"] as Label;
                
                if (string.IsNullOrEmpty(txtProduct.Text) || string.IsNullOrEmpty(txtPrice.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المنتج وسعر البيع", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                decimal price = decimal.Parse(txtPrice.Text);
                int qty = (int)nudQty.Value;
                decimal discount = decimal.TryParse(txtDiscount.Text, out decimal d) ? d : 0;
                decimal vatPercent = decimal.TryParse(txtVAT.Text, out decimal v) ? v : 15;
                
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("sp_AddInvoiceProduct_WithFixedDiscount", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@InvoiceID", 1);
                        command.Parameters.AddWithValue("@ProductID", 1); // Default product
                        command.Parameters.AddWithValue("@CostPrice", price * 0.7m); // Estimated cost
                        command.Parameters.AddWithValue("@SellingPrice", price);
                        command.Parameters.AddWithValue("@Qty", qty);
                        command.Parameters.AddWithValue("@DiscountAmount", discount);
                        command.Parameters.AddWithValue("@VATPer", vatPercent);
                        command.Parameters.AddWithValue("@Barcode", $"MANUAL_{DateTime.Now:yyyyMMddHHmmss}");
                        
                        command.ExecuteNonQuery();
                        
                        lblStatus.Text = $"✅ تم إضافة {txtProduct.Text} بخصم {discount} ريال بنجاح!";
                        lblStatus.ForeColor = Color.Green;
                        
                        // Clear form
                        txtProduct.Clear();
                        txtPrice.Clear();
                        nudQty.Value = 1;
                        txtDiscount.Text = "0";
                        
                        MessageBox.Show($"تم إضافة المنتج بنجاح!\nالمنتج: {txtProduct.Text}\nالخصم: {discount} ريال", 
                                      "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                var lblStatus = this.Controls["lblStatus"] as Label;
                lblStatus.Text = $"❌ خطأ: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnCalc_Click(object sender, EventArgs e)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("sp_CalculateInvoiceTotal", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@InvoiceID", 1);
                        var outputParam = new SqlParameter("@GrandTotal", SqlDbType.Decimal) { Direction = ParameterDirection.Output };
                        command.Parameters.Add(outputParam);
                        
                        command.ExecuteNonQuery();
                        
                        decimal grandTotal = Convert.ToDecimal(outputParam.Value);
                        
                        var lblStatus = this.Controls["lblStatus"] as Label;
                        lblStatus.Text = $"💰 إجمالي الفاتورة: {grandTotal:F2} ريال";
                        lblStatus.ForeColor = Color.DarkBlue;
                        
                        MessageBox.Show($"💰 إجمالي الفاتورة: {grandTotal:F2} ريال", 
                                      "إجمالي الفاتورة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حساب الإجمالي: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnShow_Click(object sender, EventArgs e)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    string query = @"
                        SELECT 
                            ip.IPo_ID as 'ID',
                            p.ProductName as 'المنتج',
                            ip.SellingPrice as 'السعر',
                            ip.Qty as 'الكمية',
                            ip.Amount as 'المبلغ',
                            ip.Discount as 'الخصم',
                            ip.VAT as 'الضريبة',
                            ip.TotalAmount as 'الإجمالي'
                        FROM Invoice_Product ip
                        LEFT JOIN Product p ON ip.ProductID = p.PID
                        ORDER BY ip.IPo_ID";
                    
                    using (var adapter = new SqlDataAdapter(query, connection))
                    {
                        DataTable dt = new DataTable();
                        adapter.Fill(dt);
                        
                        if (dt.Rows.Count > 0)
                        {
                            // Create a simple form to show the data
                            Form invoiceForm = new Form();
                            invoiceForm.Text = "📊 الفاتورة الحالية";
                            invoiceForm.Size = new Size(1000, 600);
                            invoiceForm.StartPosition = FormStartPosition.CenterParent;
                            invoiceForm.RightToLeft = RightToLeft.Yes;
                            
                            DataGridView dgv = new DataGridView();
                            dgv.Dock = DockStyle.Fill;
                            dgv.DataSource = dt;
                            dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                            dgv.ReadOnly = true;
                            dgv.Font = new Font("Tahoma", 10F);
                            
                            // Highlight discount column
                            dgv.Columns["الخصم"].DefaultCellStyle.BackColor = Color.LightYellow;
                            dgv.Columns["الخصم"].DefaultCellStyle.ForeColor = Color.Red;
                            dgv.Columns["الإجمالي"].DefaultCellStyle.BackColor = Color.LightGreen;
                            
                            invoiceForm.Controls.Add(dgv);
                            invoiceForm.ShowDialog();
                        }
                        else
                        {
                            MessageBox.Show("لا توجد منتجات في الفاتورة", "فاتورة فارغة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الفاتورة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
