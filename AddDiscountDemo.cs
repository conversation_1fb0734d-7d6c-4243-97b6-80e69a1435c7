using System;
using System.Data;
using Microsoft.Data.SqlClient;

class AddDiscountDemo
{
    private static string connectionString = "Data Source=.\\SQLEXPRESS;Initial Catalog=INV_DB;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True";
    
    static void Main(string[] args)
    {
        Console.OutputEncoding = System.Text.Encoding.UTF8;
        Console.Clear();
        
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║              🎯 إضافة خصم مبلغ في فاتورة المبيعات           ║");
        Console.WriteLine("║            🎯 Add Fixed Amount Discount to Invoice           ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();
        
        try
        {
            // عرض الفواتير الحالية
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("📊 الفواتير الحالية قبل إضافة الخصم:");
            Console.WriteLine("📊 Current invoices before adding discount:");
            Console.ResetColor();
            DisplayCurrentInvoices();
            
            Console.WriteLine("\n" + new string('═', 60));
            
            // إضافة منتج جديد بخصم مبلغ
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("➕ إضافة منتج جديد بخصم مبلغ ثابت:");
            Console.WriteLine("➕ Adding new product with fixed amount discount:");
            Console.ResetColor();
            
            AddProductWithFixedDiscount();
            
            Console.WriteLine("\n" + new string('═', 60));
            
            // عرض النتائج بعد الإضافة
            Console.ForegroundColor = ConsoleColor.Blue;
            Console.WriteLine("📈 النتائج بعد إضافة الخصم:");
            Console.WriteLine("📈 Results after adding discount:");
            Console.ResetColor();
            DisplayCurrentInvoices();
            
            Console.WriteLine("\n" + new string('═', 60));
            
            // تحديث خصم منتج موجود
            Console.ForegroundColor = ConsoleColor.Magenta;
            Console.WriteLine("🔄 تحديث خصم منتج موجود:");
            Console.WriteLine("🔄 Update existing product discount:");
            Console.ResetColor();
            
            UpdateExistingDiscount();
            
            Console.WriteLine("\n" + new string('═', 60));
            
            // عرض النتائج النهائية
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("🎯 النتائج النهائية:");
            Console.WriteLine("🎯 Final results:");
            Console.ResetColor();
            DisplayFinalResults();
            
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("\n✅ تم إضافة الخصومات بنجاح!");
            Console.WriteLine("✅ Discounts added successfully!");
            Console.ResetColor();
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"❌ خطأ: {ex.Message}");
            Console.ResetColor();
        }
        
        Console.WriteLine("\nاضغط أي مفتاح للخروج...");
        Console.ReadKey();
    }
    
    static void DisplayCurrentInvoices()
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            string query = @"
                SELECT 
                    ip.IPo_ID as 'ID',
                    ip.SellingPrice as 'سعر البيع',
                    ip.Qty as 'الكمية',
                    ip.Amount as 'المبلغ الأساسي',
                    ip.Discount as 'خصم المبلغ',
                    ip.VAT as 'ضريبة القيمة المضافة',
                    ip.TotalAmount as 'المجموع الإجمالي'
                FROM Invoice_Product ip
                ORDER BY ip.IPo_ID";
            
            using (var command = new SqlCommand(query, connection))
            using (var reader = command.ExecuteReader())
            {
                Console.WriteLine($"{"ID",-5} {"سعر البيع",-10} {"الكمية",-8} {"المبلغ الأساسي",-15} {"خصم المبلغ",-12} {"ضريبة ق.م",-12} {"المجموع الإجمالي",-15}");
                Console.WriteLine(new string('─', 85));
                
                decimal totalAmount = 0;
                decimal totalDiscount = 0;
                
                while (reader.Read())
                {
                    Console.WriteLine($"{reader[0],-5} {reader[1],-10} {reader[2],-8} {reader[3],-15} {reader[4],-12} {reader[5],-12} {reader[6],-15}");
                    totalAmount += Convert.ToDecimal(reader[6]);
                    totalDiscount += Convert.ToDecimal(reader[4]);
                }
                
                Console.WriteLine(new string('─', 85));
                Console.WriteLine($"💰 إجمالي الخصومات: {totalDiscount:F2} ريال");
                Console.WriteLine($"💵 إجمالي المبيعات: {totalAmount:F2} ريال");
            }
        }
    }
    
    static void AddProductWithFixedDiscount()
    {
        Console.WriteLine("📝 بيانات المنتج الجديد:");
        Console.WriteLine("📝 New product details:");
        
        // بيانات المنتج الجديد
        int invoiceId = 1;
        int productId = 6;
        decimal costPrice = 400.000m;
        decimal sellingPrice = 600.000m;
        int qty = 2;
        decimal discountAmount = 80.000m; // خصم 80 ريال
        decimal vatPer = 15.000m;
        string barcode = "DEMO002";
        
        Console.WriteLine($"🏷️  رقم الفاتورة: {invoiceId}");
        Console.WriteLine($"📦 رقم المنتج: {productId}");
        Console.WriteLine($"💰 سعر التكلفة: {costPrice} ريال");
        Console.WriteLine($"💵 سعر البيع: {sellingPrice} ريال");
        Console.WriteLine($"📊 الكمية: {qty}");
        Console.WriteLine($"🎯 خصم المبلغ: {discountAmount} ريال");
        Console.WriteLine($"📈 ضريبة القيمة المضافة: {vatPer}%");
        Console.WriteLine($"🔢 الباركود: {barcode}");
        
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            using (var command = new SqlCommand("sp_AddInvoiceProduct_WithFixedDiscount", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@InvoiceID", invoiceId);
                command.Parameters.AddWithValue("@ProductID", productId);
                command.Parameters.AddWithValue("@CostPrice", costPrice);
                command.Parameters.AddWithValue("@SellingPrice", sellingPrice);
                command.Parameters.AddWithValue("@Qty", qty);
                command.Parameters.AddWithValue("@DiscountAmount", discountAmount);
                command.Parameters.AddWithValue("@VATPer", vatPer);
                command.Parameters.AddWithValue("@Barcode", barcode);
                
                command.ExecuteNonQuery();
                
                // حساب النتائج
                decimal amount = sellingPrice * qty;
                decimal amountAfterDiscount = amount - discountAmount;
                decimal vat = amountAfterDiscount * vatPer / 100;
                decimal total = amountAfterDiscount + vat;
                
                Console.WriteLine("\n🧮 الحسابات:");
                Console.WriteLine("🧮 Calculations:");
                Console.WriteLine($"   المبلغ الأساسي = {sellingPrice} × {qty} = {amount} ريال");
                Console.WriteLine($"   المبلغ بعد الخصم = {amount} - {discountAmount} = {amountAfterDiscount} ريال");
                Console.WriteLine($"   ضريبة القيمة المضافة = {amountAfterDiscount} × {vatPer}% = {vat:F2} ريال");
                Console.WriteLine($"   المجموع الإجمالي = {amountAfterDiscount} + {vat:F2} = {total:F2} ريال");
                
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"\n✅ تم إضافة المنتج بنجاح مع خصم {discountAmount} ريال!");
                Console.WriteLine($"✅ Product added successfully with {discountAmount} SAR discount!");
                Console.ResetColor();
            }
        }
    }
    
    static void UpdateExistingDiscount()
    {
        Console.WriteLine("🔄 تحديث خصم المنتج الأول:");
        Console.WriteLine("🔄 Update first product discount:");
        
        int ipoId = 1;
        decimal newDiscountAmount = 40.000m;
        
        Console.WriteLine($"🎯 المنتج رقم: {ipoId}");
        Console.WriteLine($"💰 الخصم الجديد: {newDiscountAmount} ريال");
        
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            using (var command = new SqlCommand("sp_UpdateProductDiscount", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@IPo_ID", ipoId);
                command.Parameters.AddWithValue("@NewDiscountAmount", newDiscountAmount);
                
                command.ExecuteNonQuery();
                
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"✅ تم تحديث الخصم إلى {newDiscountAmount} ريال!");
                Console.WriteLine($"✅ Discount updated to {newDiscountAmount} SAR!");
                Console.ResetColor();
            }
        }
    }
    
    static void DisplayFinalResults()
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            
            // عرض تفاصيل الفاتورة
            string query = @"
                SELECT 
                    ip.IPo_ID,
                    ip.SellingPrice,
                    ip.Qty,
                    ip.Amount,
                    ip.Discount,
                    ip.VAT,
                    ip.TotalAmount,
                    CASE 
                        WHEN ip.Discount > 0 THEN 'خصم مبلغ ثابت'
                        ELSE 'بدون خصم'
                    END as DiscountType
                FROM Invoice_Product ip
                ORDER BY ip.IPo_ID";
            
            using (var command = new SqlCommand(query, connection))
            using (var reader = command.ExecuteReader())
            {
                Console.WriteLine($"{"ID",-5} {"السعر",-10} {"الكمية",-8} {"المبلغ",-10} {"الخصم",-10} {"الضريبة",-10} {"الإجمالي",-10} {"نوع الخصم",-15}");
                Console.WriteLine(new string('─', 90));
                
                decimal grandTotal = 0;
                decimal totalDiscounts = 0;
                int productCount = 0;
                
                while (reader.Read())
                {
                    Console.WriteLine($"{reader[0],-5} {reader[1],-10} {reader[2],-8} {reader[3],-10} {reader[4],-10} {reader[5],-10} {reader[6],-10} {reader[7],-15}");
                    grandTotal += Convert.ToDecimal(reader[6]);
                    totalDiscounts += Convert.ToDecimal(reader[4]);
                    productCount++;
                }
                
                Console.WriteLine(new string('─', 90));
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"📊 إجمالي المنتجات: {productCount}");
                Console.WriteLine($"💰 إجمالي الخصومات: {totalDiscounts:F2} ريال");
                Console.WriteLine($"💵 إجمالي الفاتورة: {grandTotal:F2} ريال");
                Console.ResetColor();
            }
        }
        
        // حساب إجمالي الفاتورة باستخدام الإجراء المخزن
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            using (var command = new SqlCommand("sp_CalculateInvoiceTotal", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@InvoiceID", 1);
                var outputParam = new SqlParameter("@GrandTotal", SqlDbType.Decimal) { Direction = ParameterDirection.Output };
                command.Parameters.Add(outputParam);
                
                command.ExecuteNonQuery();
                
                Console.ForegroundColor = ConsoleColor.Cyan;
                Console.WriteLine($"\n💰 إجمالي الفاتورة المحسوب: {outputParam.Value} ريال");
                Console.WriteLine($"💰 Calculated Invoice Total: {outputParam.Value} SAR");
                Console.ResetColor();
            }
        }
    }
}
