-- إجراءات مخزنة لنظام الخصم الجديد (مبلغ ثابت)
-- Stored Procedures for New Discount System (Fixed Amount)

USE INV_DB
GO

-- إجراء لإضافة منتج جديد للفاتورة مع خصم بالمبلغ
-- Procedure to add new product to invoice with fixed amount discount
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_AddInvoiceProduct_WithFixedDiscount')
    DROP PROCEDURE sp_AddInvoiceProduct_WithFixedDiscount
GO

CREATE PROCEDURE sp_AddInvoiceProduct_WithFixedDiscount
    @InvoiceID INT,
    @ProductID INT,
    @CostPrice DECIMAL(18,3),
    @SellingPrice DECIMAL(18,3),
    @Qty INT,
    @DiscountAmount DECIMAL(18,3) = 0,  -- خص<PERSON> بالمبلغ
    @VATPer DECIMAL(18,3) = 0,
    @Barcode NCHAR(30) = NULL
AS
BEGIN
    DECLARE @Amount DECIMAL(18,3)
    DECLARE @VAT DECIMAL(18,3)
    DECLARE @TotalAmount DECIMAL(18,3)
    DECLARE @Margin DECIMAL(18,3)
    
    -- حساب المبلغ الأساسي
    SET @Amount = @SellingPrice * @Qty
    
    -- حساب الهامش
    SET @Margin = @SellingPrice - @CostPrice
    
    -- حساب ضريبة القيمة المضافة
    SET @VAT = (@Amount - @DiscountAmount) * @VATPer / 100
    
    -- حساب المجموع الإجمالي
    SET @TotalAmount = @Amount - @DiscountAmount + @VAT
    
    -- إدراج البيانات
    INSERT INTO Invoice_Product (
        InvoiceID, ProductID, CostPrice, SellingPrice, Margin, 
        Qty, Amount, DiscountPer, Discount, VATPer, VAT, TotalAmount, Barcode
    )
    VALUES (
        @InvoiceID, @ProductID, @CostPrice, @SellingPrice, @Margin,
        @Qty, @Amount, 0, @DiscountAmount, @VATPer, @VAT, @TotalAmount, @Barcode
    )
    
    PRINT 'تم إضافة المنتج للفاتورة بنجاح مع خصم مبلغ: ' + CAST(@DiscountAmount AS VARCHAR(20))
END
GO

-- إجراء لتحديث خصم منتج موجود
-- Procedure to update discount for existing product
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_UpdateProductDiscount')
    DROP PROCEDURE sp_UpdateProductDiscount
GO

CREATE PROCEDURE sp_UpdateProductDiscount
    @IPo_ID INT,
    @NewDiscountAmount DECIMAL(18,3)
AS
BEGIN
    DECLARE @Amount DECIMAL(18,3)
    DECLARE @VATPer DECIMAL(18,3)
    DECLARE @VAT DECIMAL(18,3)
    DECLARE @TotalAmount DECIMAL(18,3)
    
    -- الحصول على البيانات الحالية
    SELECT @Amount = Amount, @VATPer = VATPer
    FROM Invoice_Product 
    WHERE IPo_ID = @IPo_ID
    
    -- حساب ضريبة القيمة المضافة الجديدة
    SET @VAT = (@Amount - @NewDiscountAmount) * @VATPer / 100
    
    -- حساب المجموع الإجمالي الجديد
    SET @TotalAmount = @Amount - @NewDiscountAmount + @VAT
    
    -- تحديث البيانات
    UPDATE Invoice_Product 
    SET 
        DiscountPer = 0,  -- إزالة النسبة المئوية
        Discount = @NewDiscountAmount,
        VAT = @VAT,
        TotalAmount = @TotalAmount
    WHERE IPo_ID = @IPo_ID
    
    PRINT 'تم تحديث الخصم بنجاح إلى: ' + CAST(@NewDiscountAmount AS VARCHAR(20))
END
GO

-- إجراء لحساب إجمالي الفاتورة
-- Procedure to calculate invoice total
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CalculateInvoiceTotal')
    DROP PROCEDURE sp_CalculateInvoiceTotal
GO

CREATE PROCEDURE sp_CalculateInvoiceTotal
    @InvoiceID INT,
    @GrandTotal DECIMAL(18,3) OUTPUT
AS
BEGIN
    SELECT @GrandTotal = SUM(TotalAmount)
    FROM Invoice_Product 
    WHERE InvoiceID = @InvoiceID
    
    -- تحديث جدول معلومات الفاتورة
    UPDATE InvoiceInfo 
    SET GrandTotal = @GrandTotal
    WHERE Inv_ID = @InvoiceID
    
    PRINT 'إجمالي الفاتورة: ' + CAST(@GrandTotal AS VARCHAR(20))
END
GO

-- إجراء لعرض تفاصيل فاتورة مع الخصومات
-- Procedure to display invoice details with discounts
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetInvoiceDetails')
    DROP PROCEDURE sp_GetInvoiceDetails
GO

CREATE PROCEDURE sp_GetInvoiceDetails
    @InvoiceID INT
AS
BEGIN
    SELECT 
        ip.IPo_ID,
        ISNULL(p.ProductName, 'غير محدد') as ProductName,
        ip.SellingPrice as 'سعر_البيع',
        ip.Qty as 'الكمية',
        ip.Amount as 'المبلغ_الأساسي',
        ip.Discount as 'خصم_المبلغ',
        ip.VAT as 'ضريبة_القيمة_المضافة',
        ip.TotalAmount as 'المجموع_الإجمالي'
    FROM Invoice_Product ip
    LEFT JOIN Product p ON ip.ProductID = p.ProductID
    WHERE ip.InvoiceID = @InvoiceID
    ORDER BY ip.IPo_ID
END
GO

PRINT 'تم إنشاء الإجراءات المخزنة بنجاح!'
PRINT 'Stored procedures created successfully!'
