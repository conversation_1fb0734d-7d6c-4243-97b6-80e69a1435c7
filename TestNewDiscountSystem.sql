-- اختبار نظام الخصم الجديد
-- Test New Discount System

USE INV_DB
GO

PRINT 'اختبار إضافة منتج جديد بخصم مبلغ ثابت'
PRINT 'Testing adding new product with fixed amount discount'

-- إضافة منتج جديد للفاتورة رقم 1 بخصم 20 ريال
EXEC sp_AddInvoiceProduct_WithFixedDiscount 
    @InvoiceID = 1,
    @ProductID = 5,
    @CostPrice = 200.000,
    @SellingPrice = 250.000,
    @Qty = 2,
    @DiscountAmount = 20.000,  -- خصم 20 ريال
    @VATPer = 15.000,          -- ضريبة 15%
    @Barcode = '10000005'

PRINT ''
PRINT 'عرض تفاصيل الفاتورة بعد الإضافة:'
PRINT 'Display invoice details after addition:'

-- عرض تفاصيل الفاتورة
EXEC sp_GetInvoiceDetails @InvoiceID = 1

PRINT ''
PRINT 'حساب إجمالي الفاتورة:'
PRINT 'Calculate invoice total:'

-- حسا<PERSON> إجمالي الفاتورة
DECLARE @Total DECIMAL(18,3)
EXEC sp_CalculateInvoiceTotal @InvoiceID = 1, @GrandTotal = @Total OUTPUT
PRINT 'إجمالي الفاتورة النهائي: ' + CAST(@Total AS VARCHAR(20))

PRINT ''
PRINT 'اختبار تحديث خصم المنتج الأول:'
PRINT 'Testing update discount for first product:'

-- تحديث خصم المنتج الأول إلى 15 ريال
EXEC sp_UpdateProductDiscount @IPo_ID = 1, @NewDiscountAmount = 15.000

PRINT ''
PRINT 'عرض البيانات النهائية:'
PRINT 'Display final data:'

-- عرض جميع منتجات الفاتورة
SELECT 
    IPo_ID as 'رقم_المنتج',
    SellingPrice as 'سعر_البيع',
    Qty as 'الكمية',
    Amount as 'المبلغ_الأساسي',
    Discount as 'خصم_المبلغ',
    VAT as 'ضريبة_القيمة_المضافة',
    TotalAmount as 'المجموع_الإجمالي'
FROM Invoice_Product 
WHERE InvoiceID = 1
ORDER BY IPo_ID

PRINT 'تم الاختبار بنجاح!'
PRINT 'Test completed successfully!'
