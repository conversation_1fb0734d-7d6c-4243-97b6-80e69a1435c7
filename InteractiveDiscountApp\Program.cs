using System;
using System.Data;
using Microsoft.Data.SqlClient;

class Program
{
    private static string connectionString = "Data Source=.\\SQLEXPRESS;Initial Catalog=INV_DB;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True";
    
    static void Main(string[] args)
    {
        Console.OutputEncoding = System.Text.Encoding.UTF8;
        Console.Clear();
        
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║           🎯 إضافة منتجات بخصومات متنوعة                   ║");
        Console.WriteLine("║         🎯 Add Products with Various Discounts              ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();
        
        try
        {
            // عرض الحالة الحالية
            DisplayCurrentStatus();
            
            // إضافة منتجات متنوعة بخصومات مختلفة
            Console.WriteLine("\n🚀 سنقوم بإضافة 3 منتجات جديدة بخصومات مختلفة:");
            Console.WriteLine("🚀 We will add 3 new products with different discounts:");
            
            // المنتج الأول: خصم صغير
            Console.WriteLine("\n" + new string('═', 60));
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("📱 المنتج الأول: سامسونج نوت 10 - خصم صغير");
            Console.WriteLine("📱 Product 1: Samsung Note 10 - Small discount");
            Console.ResetColor();
            AddProduct1();
            
            // المنتج الثاني: خصم متوسط
            Console.WriteLine("\n" + new string('═', 60));
            Console.ForegroundColor = ConsoleColor.Blue;
            Console.WriteLine("📱 المنتج الثاني: ايفون اكس - خصم متوسط");
            Console.WriteLine("📱 Product 2: iPhone X - Medium discount");
            Console.ResetColor();
            AddProduct2();
            
            // المنتج الثالث: خصم كبير
            Console.WriteLine("\n" + new string('═', 60));
            Console.ForegroundColor = ConsoleColor.Magenta;
            Console.WriteLine("📱 المنتج الثالث: سامسونج اس 20 - خصم كبير");
            Console.WriteLine("📱 Product 3: Samsung S20 - Large discount");
            Console.ResetColor();
            AddProduct3();
            
            // عرض النتائج النهائية
            Console.WriteLine("\n" + new string('═', 60));
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("📊 النتائج النهائية بعد إضافة جميع المنتجات:");
            Console.WriteLine("📊 Final results after adding all products:");
            Console.ResetColor();
            DisplayFinalResults();
            
            // تحديث بعض الخصومات
            Console.WriteLine("\n" + new string('═', 60));
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("🔄 تحديث بعض الخصومات:");
            Console.WriteLine("🔄 Update some discounts:");
            Console.ResetColor();
            UpdateSomeDiscounts();
            
            // النتائج النهائية النهائية
            Console.WriteLine("\n" + new string('═', 60));
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("🎯 النتائج النهائية بعد التحديثات:");
            Console.WriteLine("🎯 Final results after updates:");
            Console.ResetColor();
            DisplayFinalResults();
            
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("\n🎉 تم إضافة جميع المنتجات بخصومات متنوعة بنجاح!");
            Console.WriteLine("🎉 All products with various discounts added successfully!");
            Console.ResetColor();
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"❌ خطأ: {ex.Message}");
            Console.ResetColor();
        }
        
        Console.WriteLine("\nاضغط أي مفتاح للخروج...");
        Console.ReadKey();
    }
    
    static void DisplayCurrentStatus()
    {
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine("📊 الحالة الحالية للفاتورة:");
        Console.WriteLine("📊 Current invoice status:");
        Console.ResetColor();
        
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            string query = @"
                SELECT 
                    COUNT(*) as ProductCount,
                    SUM(Discount) as TotalDiscounts,
                    SUM(TotalAmount) as GrandTotal
                FROM Invoice_Product";
            
            using (var command = new SqlCommand(query, connection))
            using (var reader = command.ExecuteReader())
            {
                if (reader.Read())
                {
                    Console.WriteLine($"📦 عدد المنتجات الحالي: {reader[0]}");
                    Console.WriteLine($"💰 إجمالي الخصومات: {reader[1]} ريال");
                    Console.WriteLine($"💵 إجمالي الفاتورة: {reader[2]} ريال");
                }
            }
        }
    }
    
    static void AddProduct1()
    {
        // منتج بخصم صغير: 30 ريال
        var product = new
        {
            InvoiceID = 1,
            ProductID = 1, // سامسونج نوت 10
            CostPrice = 800.000m,
            SellingPrice = 1200.000m,
            Qty = 1,
            DiscountAmount = 30.000m, // خصم صغير
            VATPer = 15.000m,
            Barcode = "SAMSUNG_NOTE10_001"
        };
        
        Console.WriteLine($"📝 تفاصيل المنتج:");
        Console.WriteLine($"   🏷️  رقم الفاتورة: {product.InvoiceID}");
        Console.WriteLine($"   📦 رقم المنتج: {product.ProductID}");
        Console.WriteLine($"   💰 سعر التكلفة: {product.CostPrice} ريال");
        Console.WriteLine($"   💵 سعر البيع: {product.SellingPrice} ريال");
        Console.WriteLine($"   📊 الكمية: {product.Qty}");
        Console.WriteLine($"   🎯 خصم المبلغ: {product.DiscountAmount} ريال (خصم صغير)");
        Console.WriteLine($"   📈 ضريبة القيمة المضافة: {product.VATPer}%");
        
        AddProductToInvoice(product.InvoiceID, product.ProductID, product.CostPrice, 
                           product.SellingPrice, product.Qty, product.DiscountAmount, 
                           product.VATPer, product.Barcode);
        
        // حساب وعرض النتائج
        decimal amount = product.SellingPrice * product.Qty;
        decimal amountAfterDiscount = amount - product.DiscountAmount;
        decimal vat = amountAfterDiscount * product.VATPer / 100;
        decimal total = amountAfterDiscount + vat;
        
        Console.WriteLine($"\n🧮 الحسابات:");
        Console.WriteLine($"   المبلغ الأساسي = {product.SellingPrice} × {product.Qty} = {amount} ريال");
        Console.WriteLine($"   المبلغ بعد الخصم = {amount} - {product.DiscountAmount} = {amountAfterDiscount} ريال");
        Console.WriteLine($"   ضريبة القيمة المضافة = {amountAfterDiscount} × {product.VATPer}% = {vat:F2} ريال");
        Console.WriteLine($"   المجموع الإجمالي = {amountAfterDiscount} + {vat:F2} = {total:F2} ريال");
        
        Console.ForegroundColor = ConsoleColor.Green;
        Console.WriteLine($"✅ تم إضافة سامسونج نوت 10 بخصم {product.DiscountAmount} ريال!");
        Console.ResetColor();
    }
    
    static void AddProduct2()
    {
        // منتج بخصم متوسط: 150 ريال
        var product = new
        {
            InvoiceID = 1,
            ProductID = 2, // ايفون اكس
            CostPrice = 2000.000m,
            SellingPrice = 2800.000m,
            Qty = 2,
            DiscountAmount = 150.000m, // خصم متوسط
            VATPer = 15.000m,
            Barcode = "IPHONE_X_002"
        };
        
        Console.WriteLine($"📝 تفاصيل المنتج:");
        Console.WriteLine($"   🏷️  رقم الفاتورة: {product.InvoiceID}");
        Console.WriteLine($"   📦 رقم المنتج: {product.ProductID}");
        Console.WriteLine($"   💰 سعر التكلفة: {product.CostPrice} ريال");
        Console.WriteLine($"   💵 سعر البيع: {product.SellingPrice} ريال");
        Console.WriteLine($"   📊 الكمية: {product.Qty}");
        Console.WriteLine($"   🎯 خصم المبلغ: {product.DiscountAmount} ريال (خصم متوسط)");
        Console.WriteLine($"   📈 ضريبة القيمة المضافة: {product.VATPer}%");
        
        AddProductToInvoice(product.InvoiceID, product.ProductID, product.CostPrice, 
                           product.SellingPrice, product.Qty, product.DiscountAmount, 
                           product.VATPer, product.Barcode);
        
        // حساب وعرض النتائج
        decimal amount = product.SellingPrice * product.Qty;
        decimal amountAfterDiscount = amount - product.DiscountAmount;
        decimal vat = amountAfterDiscount * product.VATPer / 100;
        decimal total = amountAfterDiscount + vat;
        
        Console.WriteLine($"\n🧮 الحسابات:");
        Console.WriteLine($"   المبلغ الأساسي = {product.SellingPrice} × {product.Qty} = {amount} ريال");
        Console.WriteLine($"   المبلغ بعد الخصم = {amount} - {product.DiscountAmount} = {amountAfterDiscount} ريال");
        Console.WriteLine($"   ضريبة القيمة المضافة = {amountAfterDiscount} × {product.VATPer}% = {vat:F2} ريال");
        Console.WriteLine($"   المجموع الإجمالي = {amountAfterDiscount} + {vat:F2} = {total:F2} ريال");
        
        Console.ForegroundColor = ConsoleColor.Blue;
        Console.WriteLine($"✅ تم إضافة ايفون اكس (كمية 2) بخصم {product.DiscountAmount} ريال!");
        Console.ResetColor();
    }
    
    static void AddProduct3()
    {
        // منتج بخصم كبير: 300 ريال
        var product = new
        {
            InvoiceID = 1,
            ProductID = 4, // سامسونج اس 20
            CostPrice = 1500.000m,
            SellingPrice = 2200.000m,
            Qty = 3,
            DiscountAmount = 300.000m, // خصم كبير
            VATPer = 15.000m,
            Barcode = "SAMSUNG_S20_003"
        };
        
        Console.WriteLine($"📝 تفاصيل المنتج:");
        Console.WriteLine($"   🏷️  رقم الفاتورة: {product.InvoiceID}");
        Console.WriteLine($"   📦 رقم المنتج: {product.ProductID}");
        Console.WriteLine($"   💰 سعر التكلفة: {product.CostPrice} ريال");
        Console.WriteLine($"   💵 سعر البيع: {product.SellingPrice} ريال");
        Console.WriteLine($"   📊 الكمية: {product.Qty}");
        Console.WriteLine($"   🎯 خصم المبلغ: {product.DiscountAmount} ريال (خصم كبير)");
        Console.WriteLine($"   📈 ضريبة القيمة المضافة: {product.VATPer}%");
        
        AddProductToInvoice(product.InvoiceID, product.ProductID, product.CostPrice, 
                           product.SellingPrice, product.Qty, product.DiscountAmount, 
                           product.VATPer, product.Barcode);
        
        // حساب وعرض النتائج
        decimal amount = product.SellingPrice * product.Qty;
        decimal amountAfterDiscount = amount - product.DiscountAmount;
        decimal vat = amountAfterDiscount * product.VATPer / 100;
        decimal total = amountAfterDiscount + vat;
        
        Console.WriteLine($"\n🧮 الحسابات:");
        Console.WriteLine($"   المبلغ الأساسي = {product.SellingPrice} × {product.Qty} = {amount} ريال");
        Console.WriteLine($"   المبلغ بعد الخصم = {amount} - {product.DiscountAmount} = {amountAfterDiscount} ريال");
        Console.WriteLine($"   ضريبة القيمة المضافة = {amountAfterDiscount} × {product.VATPer}% = {vat:F2} ريال");
        Console.WriteLine($"   المجموع الإجمالي = {amountAfterDiscount} + {vat:F2} = {total:F2} ريال");
        
        Console.ForegroundColor = ConsoleColor.Magenta;
        Console.WriteLine($"✅ تم إضافة سامسونج اس 20 (كمية 3) بخصم {product.DiscountAmount} ريال!");
        Console.ResetColor();
    }
    
    static void AddProductToInvoice(int invoiceId, int productId, decimal costPrice, 
                                   decimal sellingPrice, int qty, decimal discountAmount, 
                                   decimal vatPer, string barcode)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            using (var command = new SqlCommand("sp_AddInvoiceProduct_WithFixedDiscount", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@InvoiceID", invoiceId);
                command.Parameters.AddWithValue("@ProductID", productId);
                command.Parameters.AddWithValue("@CostPrice", costPrice);
                command.Parameters.AddWithValue("@SellingPrice", sellingPrice);
                command.Parameters.AddWithValue("@Qty", qty);
                command.Parameters.AddWithValue("@DiscountAmount", discountAmount);
                command.Parameters.AddWithValue("@VATPer", vatPer);
                command.Parameters.AddWithValue("@Barcode", barcode);
                
                command.ExecuteNonQuery();
            }
        }
    }
    
    static void UpdateSomeDiscounts()
    {
        Console.WriteLine("🔄 تحديث خصم المنتج الأول إلى 75 ريال:");
        UpdateProductDiscount(1, 75.000m);
        
        Console.WriteLine("🔄 تحديث خصم المنتج الثالث إلى 45 ريال:");
        UpdateProductDiscount(3, 45.000m);
        
        Console.WriteLine("🔄 تحديث خصم المنتج السادس إلى 120 ريال:");
        UpdateProductDiscount(6, 120.000m);
    }
    
    static void UpdateProductDiscount(int ipoId, decimal newDiscountAmount)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            using (var command = new SqlCommand("sp_UpdateProductDiscount", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@IPo_ID", ipoId);
                command.Parameters.AddWithValue("@NewDiscountAmount", newDiscountAmount);
                
                command.ExecuteNonQuery();
                
                Console.WriteLine($"   ✅ تم تحديث خصم المنتج {ipoId} إلى {newDiscountAmount} ريال");
            }
        }
    }
    
    static void DisplayFinalResults()
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            
            // عرض تفاصيل الفاتورة
            string query = @"
                SELECT 
                    ip.IPo_ID,
                    p.ProductName,
                    ip.SellingPrice,
                    ip.Qty,
                    ip.Amount,
                    ip.Discount,
                    ip.VAT,
                    ip.TotalAmount,
                    CASE 
                        WHEN ip.Discount <= 50 THEN 'خصم صغير'
                        WHEN ip.Discount <= 150 THEN 'خصم متوسط'
                        ELSE 'خصم كبير'
                    END as DiscountLevel
                FROM Invoice_Product ip
                LEFT JOIN Product p ON ip.ProductID = p.PID
                ORDER BY ip.IPo_ID";
            
            using (var command = new SqlCommand(query, connection))
            using (var reader = command.ExecuteReader())
            {
                Console.WriteLine($"{"ID",-5} {"المنتج",-20} {"السعر",-10} {"الكمية",-8} {"المبلغ",-10} {"الخصم",-10} {"الضريبة",-10} {"الإجمالي",-10} {"مستوى الخصم",-12}");
                Console.WriteLine(new string('─', 115));
                
                decimal grandTotal = 0;
                decimal totalDiscounts = 0;
                int productCount = 0;
                
                while (reader.Read())
                {
                    Console.WriteLine($"{reader[0],-5} {reader[1],-20} {reader[2],-10} {reader[3],-8} {reader[4],-10} {reader[5],-10} {reader[6],-10} {reader[7],-10} {reader[8],-12}");
                    grandTotal += Convert.ToDecimal(reader[7]);
                    totalDiscounts += Convert.ToDecimal(reader[5]);
                    productCount++;
                }
                
                Console.WriteLine(new string('─', 115));
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"📊 إجمالي المنتجات: {productCount}");
                Console.WriteLine($"💰 إجمالي الخصومات: {totalDiscounts:F2} ريال");
                Console.WriteLine($"💵 إجمالي الفاتورة: {grandTotal:F2} ريال");
                Console.ResetColor();
            }
        }
        
        // حساب إجمالي الفاتورة باستخدام الإجراء المخزن
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            using (var command = new SqlCommand("sp_CalculateInvoiceTotal", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@InvoiceID", 1);
                var outputParam = new SqlParameter("@GrandTotal", SqlDbType.Decimal) { Direction = ParameterDirection.Output };
                command.Parameters.Add(outputParam);
                
                command.ExecuteNonQuery();
                
                Console.ForegroundColor = ConsoleColor.Cyan;
                Console.WriteLine($"\n💰 إجمالي الفاتورة المحسوب: {outputParam.Value} ريال");
                Console.WriteLine($"💰 Calculated Invoice Total: {outputParam.Value} SAR");
                Console.ResetColor();
            }
        }
        
        // إحصائيات الخصومات
        DisplayDiscountStatistics();
    }
    
    static void DisplayDiscountStatistics()
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            string query = @"
                SELECT 
                    COUNT(CASE WHEN Discount <= 50 THEN 1 END) as SmallDiscounts,
                    COUNT(CASE WHEN Discount > 50 AND Discount <= 150 THEN 1 END) as MediumDiscounts,
                    COUNT(CASE WHEN Discount > 150 THEN 1 END) as LargeDiscounts,
                    AVG(Discount) as AvgDiscount,
                    MAX(Discount) as MaxDiscount,
                    MIN(Discount) as MinDiscount
                FROM Invoice_Product
                WHERE Discount > 0";
            
            using (var command = new SqlCommand(query, connection))
            using (var reader = command.ExecuteReader())
            {
                if (reader.Read())
                {
                    Console.WriteLine("\n📈 إحصائيات الخصومات:");
                    Console.WriteLine("📈 Discount Statistics:");
                    Console.WriteLine($"   🟢 خصومات صغيرة (≤50): {reader[0]} منتج");
                    Console.WriteLine($"   🟡 خصومات متوسطة (51-150): {reader[1]} منتج");
                    Console.WriteLine($"   🔴 خصومات كبيرة (>150): {reader[2]} منتج");
                    Console.WriteLine($"   📊 متوسط الخصم: {Convert.ToDecimal(reader[3]):F2} ريال");
                    Console.WriteLine($"   ⬆️ أعلى خصم: {reader[4]} ريال");
                    Console.WriteLine($"   ⬇️ أقل خصم: {reader[5]} ريال");
                }
            }
        }
    }
}
