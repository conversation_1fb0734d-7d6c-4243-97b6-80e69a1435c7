using System;
using System.Data;
using Microsoft.Data.SqlClient;

class Program
{
    private static string connectionString = "Data Source=.\\SQLEXPRESS;Initial Catalog=INV_DB;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True";
    
    static void Main(string[] args)
    {
        Console.OutputEncoding = System.Text.Encoding.UTF8;
        Console.Clear();
        
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    🎯 عرض توضيحي لنظام الخصم                ║");
        Console.WriteLine("║                   🎯 Discount System Demo                    ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();
        
        try
        {
            // 1. عرض البيانات الحالية
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("📊 الخطوة 1: عرض البيانات الحالية");
            Console.WriteLine("📊 Step 1: Display Current Data");
            Console.ResetColor();
            DisplayCurrentData();
            
            Console.WriteLine("\nاضغط Enter للمتابعة...");
            Console.ReadLine();
            
            // 2. إضافة منتج جديد بخصم مبلغ ثابت
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("\n➕ الخطوة 2: إضافة منتج جديد بخصم 60 ريال");
            Console.WriteLine("➕ Step 2: Add New Product with 60 SAR Discount");
            Console.ResetColor();
            AddProductDemo();
            
            Console.WriteLine("\nاضغط Enter للمتابعة...");
            Console.ReadLine();
            
            // 3. عرض النتائج بعد الإضافة
            Console.ForegroundColor = ConsoleColor.Blue;
            Console.WriteLine("\n📈 الخطوة 3: عرض النتائج بعد الإضافة");
            Console.WriteLine("📈 Step 3: Display Results After Addition");
            Console.ResetColor();
            DisplayCurrentData();
            
            Console.WriteLine("\nاضغط Enter للمتابعة...");
            Console.ReadLine();
            
            // 4. تحديث خصم منتج موجود
            Console.ForegroundColor = ConsoleColor.Magenta;
            Console.WriteLine("\n🔄 الخطوة 4: تحديث خصم المنتج الأول إلى 25 ريال");
            Console.WriteLine("🔄 Step 4: Update First Product Discount to 25 SAR");
            Console.ResetColor();
            UpdateDiscountDemo();
            
            Console.WriteLine("\nاضغط Enter للمتابعة...");
            Console.ReadLine();
            
            // 5. عرض النتائج النهائية
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("\n🎯 الخطوة 5: النتائج النهائية");
            Console.WriteLine("🎯 Step 5: Final Results");
            Console.ResetColor();
            DisplayFinalResults();
            
            // 6. حساب إجمالي الفاتورة
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("\n💰 الخطوة 6: حساب إجمالي الفاتورة");
            Console.WriteLine("💰 Step 6: Calculate Invoice Total");
            Console.ResetColor();
            CalculateTotal();
            
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("\n✅ تم العرض التوضيحي بنجاح!");
            Console.WriteLine("✅ Demo completed successfully!");
            Console.WriteLine("\n🎉 نظام الخصم الجديد يعمل بشكل مثالي!");
            Console.WriteLine("🎉 New discount system works perfectly!");
            Console.ResetColor();
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"❌ خطأ: {ex.Message}");
            Console.ResetColor();
        }
        
        Console.WriteLine("\nاضغط أي مفتاح للخروج...");
        Console.ReadKey();
    }
    
    static void DisplayCurrentData()
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            string query = @"
                SELECT 
                    IPo_ID,
                    SellingPrice,
                    Qty,
                    Amount,
                    DiscountPer,
                    Discount,
                    VAT,
                    TotalAmount
                FROM Invoice_Product 
                ORDER BY IPo_ID";
            
            using (var command = new SqlCommand(query, connection))
            using (var reader = command.ExecuteReader())
            {
                Console.WriteLine($"{"ID",-5} {"Price",-10} {"Qty",-5} {"Amount",-10} {"Disc%",-8} {"DiscAmt",-10} {"VAT",-8} {"Total",-10}");
                Console.WriteLine(new string('─', 70));
                
                decimal grandTotal = 0;
                while (reader.Read())
                {
                    Console.WriteLine($"{reader[0],-5} {reader[1],-10} {reader[2],-5} {reader[3],-10} {reader[4],-8} {reader[5],-10} {reader[6],-8} {reader[7],-10}");
                    grandTotal += Convert.ToDecimal(reader[7]);
                }
                
                Console.WriteLine(new string('─', 70));
                Console.WriteLine($"💰 إجمالي: {grandTotal:F2} ريال");
            }
        }
    }
    
    static void AddProductDemo()
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            using (var command = new SqlCommand("sp_AddInvoiceProduct_WithFixedDiscount", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@InvoiceID", 1);
                command.Parameters.AddWithValue("@ProductID", 5);
                command.Parameters.AddWithValue("@CostPrice", 350.000);
                command.Parameters.AddWithValue("@SellingPrice", 500.000);
                command.Parameters.AddWithValue("@Qty", 1);
                command.Parameters.AddWithValue("@DiscountAmount", 60.000);  // خصم 60 ريال
                command.Parameters.AddWithValue("@VATPer", 15.000);
                command.Parameters.AddWithValue("@Barcode", "DEMO001");
                
                command.ExecuteNonQuery();
                
                Console.WriteLine("✅ تم إضافة منتج بسعر 500 ريال مع خصم 60 ريال");
                Console.WriteLine("✅ Added product: Price 500 SAR with 60 SAR discount");
                Console.WriteLine($"✅ المبلغ بعد الخصم: {500 - 60} ريال");
                Console.WriteLine($"✅ ضريبة القيمة المضافة: {(500 - 60) * 0.15:F2} ريال");
                Console.WriteLine($"✅ المجموع الإجمالي: {(500 - 60) + ((500 - 60) * 0.15):F2} ريال");
            }
        }
    }
    
    static void UpdateDiscountDemo()
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            using (var command = new SqlCommand("sp_UpdateProductDiscount", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@IPo_ID", 1);
                command.Parameters.AddWithValue("@NewDiscountAmount", 25.000);
                
                command.ExecuteNonQuery();
                
                Console.WriteLine("✅ تم تحديث خصم المنتج الأول من 15 ريال إلى 25 ريال");
                Console.WriteLine("✅ Updated first product discount from 15 SAR to 25 SAR");
            }
        }
    }
    
    static void DisplayFinalResults()
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            string query = @"
                SELECT 
                    IPo_ID,
                    SellingPrice,
                    Qty,
                    Amount,
                    Discount,
                    VAT,
                    TotalAmount,
                    CASE 
                        WHEN Discount > 0 THEN 'خصم مبلغ ثابت'
                        ELSE 'بدون خصم'
                    END as DiscountType
                FROM Invoice_Product 
                ORDER BY IPo_ID";
            
            using (var command = new SqlCommand(query, connection))
            using (var reader = command.ExecuteReader())
            {
                Console.WriteLine($"{"ID",-5} {"Price",-10} {"Qty",-5} {"Amount",-10} {"Discount",-10} {"VAT",-8} {"Total",-10} {"Type",-15}");
                Console.WriteLine(new string('─', 85));
                
                decimal grandTotal = 0;
                while (reader.Read())
                {
                    Console.WriteLine($"{reader[0],-5} {reader[1],-10} {reader[2],-5} {reader[3],-10} {reader[4],-10} {reader[5],-8} {reader[6],-10} {reader[7],-15}");
                    grandTotal += Convert.ToDecimal(reader[6]);
                }
                
                Console.WriteLine(new string('─', 85));
                Console.WriteLine($"💰 إجمالي الفاتورة النهائي: {grandTotal:F2} ريال");
            }
        }
    }
    
    static void CalculateTotal()
    {
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            using (var command = new SqlCommand("sp_CalculateInvoiceTotal", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@InvoiceID", 1);
                var outputParam = new SqlParameter("@GrandTotal", SqlDbType.Decimal) { Direction = ParameterDirection.Output };
                command.Parameters.Add(outputParam);
                
                command.ExecuteNonQuery();
                
                Console.WriteLine($"💰 إجمالي الفاتورة المحسوب: {outputParam.Value} ريال");
                Console.WriteLine($"💰 Calculated Invoice Total: {outputParam.Value} SAR");
            }
        }
    }
}
