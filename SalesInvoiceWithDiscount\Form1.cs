using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Data.SqlClient;

namespace SalesInvoiceWithDiscount
{
    public partial class Form1 : Form
    {
        private string connectionString = "Data Source=.\\SQLEXPRESS;Initial Catalog=INV_DB;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True";
        
        // Controls
        private ComboBox cmbProduct;
        private TextBox txtCostPrice;
        private TextBox txtSellingPrice;
        private NumericUpDown nudQuantity;
        private TextBox txtDiscountAmount;
        private TextBox txtVATPercent;
        private TextBox txtBarcode;
        private Button btnAddProduct;
        private Button btnCalculateTotal;
        private DataGridView dgvInvoice;
        private Label lblGrandTotal;
        private Label lblTotalDiscount;
        
        public Form1()
        {
            InitializeComponent();
            LoadProducts();
            SetupDataGridView();
        }
        
        private void InitializeComponent()
        {
            this.Text = "فاتورة المبيعات مع خصم المبلغ - Sales Invoice with Fixed Discount";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.Font = new Font("Tahoma", 10F);
            
            // Create main panel
            Panel mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Padding = new Padding(10);
            this.Controls.Add(mainPanel);
            
            // Title
            Label lblTitle = new Label();
            lblTitle.Text = "🎯 فاتورة المبيعات مع خصم المبلغ الثابت";
            lblTitle.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            lblTitle.ForeColor = Color.DarkBlue;
            lblTitle.AutoSize = true;
            lblTitle.Location = new Point(10, 10);
            mainPanel.Controls.Add(lblTitle);
            
            // Product selection group
            GroupBox grpProduct = new GroupBox();
            grpProduct.Text = "بيانات المنتج - Product Details";
            grpProduct.Location = new Point(10, 50);
            grpProduct.Size = new Size(1160, 200);
            grpProduct.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            mainPanel.Controls.Add(grpProduct);
            
            // Product combo
            Label lblProduct = new Label();
            lblProduct.Text = "المنتج:";
            lblProduct.Location = new Point(1000, 30);
            lblProduct.AutoSize = true;
            grpProduct.Controls.Add(lblProduct);
            
            cmbProduct = new ComboBox();
            cmbProduct.Location = new Point(750, 27);
            cmbProduct.Size = new Size(240, 25);
            cmbProduct.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbProduct.SelectedIndexChanged += CmbProduct_SelectedIndexChanged;
            grpProduct.Controls.Add(cmbProduct);
            
            // Cost Price
            Label lblCostPrice = new Label();
            lblCostPrice.Text = "سعر التكلفة:";
            lblCostPrice.Location = new Point(650, 30);
            lblCostPrice.AutoSize = true;
            grpProduct.Controls.Add(lblCostPrice);
            
            txtCostPrice = new TextBox();
            txtCostPrice.Location = new Point(500, 27);
            txtCostPrice.Size = new Size(140, 25);
            grpProduct.Controls.Add(txtCostPrice);
            
            // Selling Price
            Label lblSellingPrice = new Label();
            lblSellingPrice.Text = "سعر البيع:";
            lblSellingPrice.Location = new Point(420, 30);
            lblSellingPrice.AutoSize = true;
            grpProduct.Controls.Add(lblSellingPrice);
            
            txtSellingPrice = new TextBox();
            txtSellingPrice.Location = new Point(270, 27);
            txtSellingPrice.Size = new Size(140, 25);
            txtSellingPrice.TextChanged += TxtSellingPrice_TextChanged;
            grpProduct.Controls.Add(txtSellingPrice);
            
            // Quantity
            Label lblQuantity = new Label();
            lblQuantity.Text = "الكمية:";
            lblQuantity.Location = new Point(200, 30);
            lblQuantity.AutoSize = true;
            grpProduct.Controls.Add(lblQuantity);
            
            nudQuantity = new NumericUpDown();
            nudQuantity.Location = new Point(120, 27);
            nudQuantity.Size = new Size(70, 25);
            nudQuantity.Minimum = 1;
            nudQuantity.Value = 1;
            nudQuantity.ValueChanged += NudQuantity_ValueChanged;
            grpProduct.Controls.Add(nudQuantity);
            
            // Discount Amount (NEW FEATURE)
            Label lblDiscountAmount = new Label();
            lblDiscountAmount.Text = "🎯 خصم المبلغ (ريال):";
            lblDiscountAmount.Location = new Point(1000, 70);
            lblDiscountAmount.AutoSize = true;
            lblDiscountAmount.ForeColor = Color.Red;
            lblDiscountAmount.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            grpProduct.Controls.Add(lblDiscountAmount);
            
            txtDiscountAmount = new TextBox();
            txtDiscountAmount.Location = new Point(850, 67);
            txtDiscountAmount.Size = new Size(140, 25);
            txtDiscountAmount.Text = "0";
            txtDiscountAmount.BackColor = Color.LightYellow;
            txtDiscountAmount.TextChanged += TxtDiscountAmount_TextChanged;
            grpProduct.Controls.Add(txtDiscountAmount);
            
            // VAT Percentage
            Label lblVAT = new Label();
            lblVAT.Text = "ضريبة القيمة المضافة (%):";
            lblVAT.Location = new Point(650, 70);
            lblVAT.AutoSize = true;
            grpProduct.Controls.Add(lblVAT);
            
            txtVATPercent = new TextBox();
            txtVATPercent.Location = new Point(500, 67);
            txtVATPercent.Size = new Size(140, 25);
            txtVATPercent.Text = "15";
            grpProduct.Controls.Add(txtVATPercent);
            
            // Barcode
            Label lblBarcode = new Label();
            lblBarcode.Text = "الباركود:";
            lblBarcode.Location = new Point(420, 70);
            lblBarcode.AutoSize = true;
            grpProduct.Controls.Add(lblBarcode);
            
            txtBarcode = new TextBox();
            txtBarcode.Location = new Point(270, 67);
            txtBarcode.Size = new Size(140, 25);
            grpProduct.Controls.Add(txtBarcode);
            
            // Calculation display
            Label lblCalculation = new Label();
            lblCalculation.Text = "💰 الحسابات التلقائية:";
            lblCalculation.Location = new Point(1000, 110);
            lblCalculation.AutoSize = true;
            lblCalculation.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblCalculation.ForeColor = Color.DarkGreen;
            grpProduct.Controls.Add(lblCalculation);
            
            // Add Product Button
            btnAddProduct = new Button();
            btnAddProduct.Text = "➕ إضافة المنتج للفاتورة";
            btnAddProduct.Location = new Point(20, 150);
            btnAddProduct.Size = new Size(200, 35);
            btnAddProduct.BackColor = Color.LightGreen;
            btnAddProduct.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnAddProduct.Click += BtnAddProduct_Click;
            grpProduct.Controls.Add(btnAddProduct);
            
            // Calculate Total Button
            btnCalculateTotal = new Button();
            btnCalculateTotal.Text = "🧮 حساب إجمالي الفاتورة";
            btnCalculateTotal.Location = new Point(240, 150);
            btnCalculateTotal.Size = new Size(200, 35);
            btnCalculateTotal.BackColor = Color.LightBlue;
            btnCalculateTotal.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnCalculateTotal.Click += BtnCalculateTotal_Click;
            grpProduct.Controls.Add(btnCalculateTotal);
            
            // DataGridView for invoice items
            dgvInvoice = new DataGridView();
            dgvInvoice.Location = new Point(10, 270);
            dgvInvoice.Size = new Size(1160, 350);
            dgvInvoice.AllowUserToAddRows = false;
            dgvInvoice.AllowUserToDeleteRows = false;
            dgvInvoice.ReadOnly = true;
            dgvInvoice.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvInvoice.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            mainPanel.Controls.Add(dgvInvoice);
            
            // Totals
            Panel pnlTotals = new Panel();
            pnlTotals.Location = new Point(10, 640);
            pnlTotals.Size = new Size(1160, 100);
            pnlTotals.BackColor = Color.LightGray;
            pnlTotals.BorderStyle = BorderStyle.FixedSingle;
            mainPanel.Controls.Add(pnlTotals);
            
            lblTotalDiscount = new Label();
            lblTotalDiscount.Text = "💰 إجمالي الخصومات: 0.00 ريال";
            lblTotalDiscount.Location = new Point(800, 20);
            lblTotalDiscount.AutoSize = true;
            lblTotalDiscount.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            lblTotalDiscount.ForeColor = Color.Red;
            pnlTotals.Controls.Add(lblTotalDiscount);
            
            lblGrandTotal = new Label();
            lblGrandTotal.Text = "💵 إجمالي الفاتورة: 0.00 ريال";
            lblGrandTotal.Location = new Point(800, 50);
            lblGrandTotal.AutoSize = true;
            lblGrandTotal.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            lblGrandTotal.ForeColor = Color.DarkBlue;
            pnlTotals.Controls.Add(lblGrandTotal);
        }
        
        private void LoadProducts()
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    string query = "SELECT PID, ProductName FROM Product ORDER BY ProductName";
                    
                    using (var command = new SqlCommand(query, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        DataTable dt = new DataTable();
                        dt.Load(reader);
                        
                        cmbProduct.DisplayMember = "ProductName";
                        cmbProduct.ValueMember = "PID";
                        cmbProduct.DataSource = dt;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void SetupDataGridView()
        {
            dgvInvoice.Columns.Clear();
            dgvInvoice.Columns.Add("ID", "ID");
            dgvInvoice.Columns.Add("ProductName", "اسم المنتج");
            dgvInvoice.Columns.Add("CostPrice", "سعر التكلفة");
            dgvInvoice.Columns.Add("SellingPrice", "سعر البيع");
            dgvInvoice.Columns.Add("Quantity", "الكمية");
            dgvInvoice.Columns.Add("Amount", "المبلغ الأساسي");
            dgvInvoice.Columns.Add("DiscountAmount", "خصم المبلغ");
            dgvInvoice.Columns.Add("VAT", "ضريبة ق.م");
            dgvInvoice.Columns.Add("TotalAmount", "المجموع الإجمالي");
            dgvInvoice.Columns.Add("DiscountType", "نوع الخصم");
            
            // Set column colors
            dgvInvoice.Columns["DiscountAmount"].DefaultCellStyle.BackColor = Color.LightYellow;
            dgvInvoice.Columns["DiscountAmount"].DefaultCellStyle.ForeColor = Color.Red;
            dgvInvoice.Columns["TotalAmount"].DefaultCellStyle.BackColor = Color.LightGreen;
            dgvInvoice.Columns["TotalAmount"].DefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
        }
        
        private void CmbProduct_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbProduct.SelectedValue != null)
            {
                // Auto-generate barcode based on product
                txtBarcode.Text = $"PRD_{cmbProduct.SelectedValue}_{DateTime.Now:yyyyMMdd}";
            }
        }
        
        private void TxtSellingPrice_TextChanged(object sender, EventArgs e)
        {
            UpdateCalculations();
        }
        
        private void NudQuantity_ValueChanged(object sender, EventArgs e)
        {
            UpdateCalculations();
        }
        
        private void TxtDiscountAmount_TextChanged(object sender, EventArgs e)
        {
            UpdateCalculations();
        }
        
        private void UpdateCalculations()
        {
            try
            {
                decimal sellingPrice = decimal.TryParse(txtSellingPrice.Text, out decimal sp) ? sp : 0;
                int quantity = (int)nudQuantity.Value;
                decimal discountAmount = decimal.TryParse(txtDiscountAmount.Text, out decimal da) ? da : 0;
                decimal vatPercent = decimal.TryParse(txtVATPercent.Text, out decimal vp) ? vp : 15;
                
                decimal amount = sellingPrice * quantity;
                decimal amountAfterDiscount = amount - discountAmount;
                decimal vat = amountAfterDiscount * vatPercent / 100;
                decimal total = amountAfterDiscount + vat;
                
                // Update the calculation display in the group box
                foreach (Control control in this.Controls[0].Controls)
                {
                    if (control is GroupBox grp && grp.Text.Contains("Product"))
                    {
                        // Remove old calculation labels
                        var labelsToRemove = new List<Control>();
                        foreach (Control c in grp.Controls)
                        {
                            if (c is Label lbl && lbl.Text.Contains("="))
                                labelsToRemove.Add(c);
                        }
                        foreach (var lbl in labelsToRemove)
                            grp.Controls.Remove(lbl);
                        
                        // Add new calculation labels
                        Label lblCalc1 = new Label();
                        lblCalc1.Text = $"المبلغ الأساسي = {sellingPrice} × {quantity} = {amount:F2} ريال";
                        lblCalc1.Location = new Point(500, 110);
                        lblCalc1.AutoSize = true;
                        lblCalc1.ForeColor = Color.DarkBlue;
                        grp.Controls.Add(lblCalc1);
                        
                        Label lblCalc2 = new Label();
                        lblCalc2.Text = $"بعد الخصم = {amount:F2} - {discountAmount:F2} = {amountAfterDiscount:F2} ريال";
                        lblCalc2.Location = new Point(500, 130);
                        lblCalc2.AutoSize = true;
                        lblCalc2.ForeColor = Color.Red;
                        grp.Controls.Add(lblCalc2);
                        
                        Label lblCalc3 = new Label();
                        lblCalc3.Text = $"الإجمالي النهائي = {amountAfterDiscount:F2} + {vat:F2} = {total:F2} ريال";
                        lblCalc3.Location = new Point(20, 110);
                        lblCalc3.AutoSize = true;
                        lblCalc3.ForeColor = Color.DarkGreen;
                        lblCalc3.Font = new Font("Tahoma", 10F, FontStyle.Bold);
                        grp.Controls.Add(lblCalc3);
                        
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                // Ignore calculation errors during typing
            }
        }
        
        private void BtnAddProduct_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbProduct.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار منتج", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                int productId = Convert.ToInt32(cmbProduct.SelectedValue);
                decimal costPrice = decimal.TryParse(txtCostPrice.Text, out decimal cp) ? cp : 0;
                decimal sellingPrice = decimal.TryParse(txtSellingPrice.Text, out decimal sp) ? sp : 0;
                int quantity = (int)nudQuantity.Value;
                decimal discountAmount = decimal.TryParse(txtDiscountAmount.Text, out decimal da) ? da : 0;
                decimal vatPercent = decimal.TryParse(txtVATPercent.Text, out decimal vp) ? vp : 15;
                string barcode = txtBarcode.Text;
                
                if (sellingPrice <= 0)
                {
                    MessageBox.Show("يرجى إدخال سعر البيع", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("sp_AddInvoiceProduct_WithFixedDiscount", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@InvoiceID", 1);
                        command.Parameters.AddWithValue("@ProductID", productId);
                        command.Parameters.AddWithValue("@CostPrice", costPrice);
                        command.Parameters.AddWithValue("@SellingPrice", sellingPrice);
                        command.Parameters.AddWithValue("@Qty", quantity);
                        command.Parameters.AddWithValue("@DiscountAmount", discountAmount);
                        command.Parameters.AddWithValue("@VATPer", vatPercent);
                        command.Parameters.AddWithValue("@Barcode", barcode);
                        
                        command.ExecuteNonQuery();
                        
                        MessageBox.Show($"تم إضافة المنتج بنجاح مع خصم {discountAmount} ريال!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        // Clear form
                        txtCostPrice.Clear();
                        txtSellingPrice.Clear();
                        nudQuantity.Value = 1;
                        txtDiscountAmount.Text = "0";
                        txtBarcode.Clear();
                        
                        // Refresh invoice display
                        LoadInvoiceData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnCalculateTotal_Click(object sender, EventArgs e)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("sp_CalculateInvoiceTotal", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@InvoiceID", 1);
                        var outputParam = new SqlParameter("@GrandTotal", SqlDbType.Decimal) { Direction = ParameterDirection.Output };
                        command.Parameters.Add(outputParam);
                        
                        command.ExecuteNonQuery();
                        
                        decimal grandTotal = Convert.ToDecimal(outputParam.Value);
                        
                        MessageBox.Show($"إجمالي الفاتورة: {grandTotal:F2} ريال", "إجمالي الفاتورة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        LoadInvoiceData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حساب الإجمالي: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void LoadInvoiceData()
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    string query = @"
                        SELECT 
                            ip.IPo_ID,
                            p.ProductName,
                            ip.CostPrice,
                            ip.SellingPrice,
                            ip.Qty,
                            ip.Amount,
                            ip.Discount,
                            ip.VAT,
                            ip.TotalAmount,
                            CASE 
                                WHEN ip.Discount > 0 THEN 'خصم مبلغ ثابت'
                                ELSE 'بدون خصم'
                            END as DiscountType
                        FROM Invoice_Product ip
                        LEFT JOIN Product p ON ip.ProductID = p.PID
                        ORDER BY ip.IPo_ID";
                    
                    using (var adapter = new SqlDataAdapter(query, connection))
                    {
                        DataTable dt = new DataTable();
                        adapter.Fill(dt);
                        dgvInvoice.DataSource = dt;
                        
                        // Calculate totals
                        decimal totalDiscount = 0;
                        decimal grandTotal = 0;
                        
                        foreach (DataRow row in dt.Rows)
                        {
                            totalDiscount += Convert.ToDecimal(row["Discount"]);
                            grandTotal += Convert.ToDecimal(row["TotalAmount"]);
                        }
                        
                        lblTotalDiscount.Text = $"💰 إجمالي الخصومات: {totalDiscount:F2} ريال";
                        lblGrandTotal.Text = $"💵 إجمالي الفاتورة: {grandTotal:F2} ريال";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الفاتورة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void Form1_Load(object sender, EventArgs e)
        {
            LoadInvoiceData();
        }
    }
}
