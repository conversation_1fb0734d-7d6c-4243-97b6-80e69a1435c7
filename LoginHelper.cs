using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Threading;

class LoginHelper
{
    private static string connectionString = "Data Source=.\\SQLEXPRESS;Initial Catalog=INV_DB;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True";
    private static string username = "admin";
    private static string password = "12345";
    
    static void Main(string[] args)
    {
        Console.OutputEncoding = System.Text.Encoding.UTF8;
        Console.Clear();
        
        DisplayWelcomeScreen();
        
        // محاولة الدخول
        if (AttemptLogin())
        {
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("✅ تم تسجيل الدخول بنجاح!");
            Console.WriteLine("✅ Login successful!");
            Console.ResetColor();
            
            // عرض القائمة الرئيسية
            ShowMainMenu();
        }
        else
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine("❌ فشل في تسجيل الدخول!");
            Console.WriteLine("❌ Login failed!");
            Console.ResetColor();
        }
        
        Console.WriteLine("\nاضغط أي مفتاح للخروج...");
        Console.ReadKey();
    }
    
    static void DisplayWelcomeScreen()
    {
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                برنامج المخزون والمحاسبة                    ║");
        Console.WriteLine("║              Inventory & Accounting System                  ║");
        Console.WriteLine("║                                                              ║");
        Console.WriteLine("║                  🔐 تسجيل الدخول                           ║");
        Console.WriteLine("║                    🔐 Login                                  ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();
    }
    
    static bool AttemptLogin()
    {
        Console.WriteLine("🔐 محاولة تسجيل الدخول...");
        Console.WriteLine("🔐 Attempting login...");
        Console.WriteLine($"👤 اسم المستخدم: {username}");
        Console.WriteLine($"👤 Username: {username}");
        Console.WriteLine($"🔑 كلمة المرور: {new string('*', password.Length)}");
        Console.WriteLine($"🔑 Password: {new string('*', password.Length)}");
        
        // محاكاة عملية تسجيل الدخول
        for (int i = 1; i <= 3; i++)
        {
            Console.Write($"⏳ جاري التحقق... {i}/3");
            Thread.Sleep(1000);
            Console.Write("\r" + new string(' ', 50) + "\r");
        }
        
        // التحقق من الاتصال بقاعدة البيانات
        try
        {
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine("✅ تم الاتصال بقاعدة البيانات بنجاح");
                Console.WriteLine("✅ Database connection successful");
                return true;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في الاتصال: {ex.Message}");
            return false;
        }
    }
    
    static void ShowMainMenu()
    {
        bool continueRunning = true;
        
        while (continueRunning)
        {
            Console.Clear();
            DisplayHeader();
            DisplayMenuOptions();
            
            Console.Write("اختر رقماً من القائمة - Choose a number: ");
            string choice = Console.ReadLine();
            
            switch (choice)
            {
                case "1":
                    ShowInvoiceManagement();
                    break;
                case "2":
                    ShowDiscountSystem();
                    break;
                case "3":
                    ShowReports();
                    break;
                case "4":
                    ShowSettings();
                    break;
                case "5":
                    ShowBackupRestore();
                    break;
                case "0":
                    continueRunning = false;
                    Console.WriteLine("🙏 شكراً لاستخدام البرنامج!");
                    Console.WriteLine("🙏 Thank you for using the system!");
                    break;
                default:
                    Console.WriteLine("❌ اختيار غير صحيح");
                    Console.WriteLine("❌ Invalid choice");
                    break;
            }
            
            if (continueRunning)
            {
                Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                Console.ReadKey();
            }
        }
    }
    
    static void DisplayHeader()
    {
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine("🏢 برنامج المخزون والمحاسبة - مرحباً " + username);
        Console.WriteLine("🏢 Inventory & Accounting System - Welcome " + username);
        Console.WriteLine("═══════════════════════════════════════════════════════════");
        Console.ResetColor();
    }
    
    static void DisplayMenuOptions()
    {
        Console.WriteLine("1️⃣  إدارة الفواتير - Invoice Management");
        Console.WriteLine("2️⃣  نظام الخصم الجديد - New Discount System");
        Console.WriteLine("3️⃣  التقارير - Reports");
        Console.WriteLine("4️⃣  الإعدادات - Settings");
        Console.WriteLine("5️⃣  النسخ الاحتياطي - Backup & Restore");
        Console.WriteLine("0️⃣  خروج - Exit");
        Console.WriteLine();
    }
    
    static void ShowInvoiceManagement()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Green;
        Console.WriteLine("📋 إدارة الفواتير - Invoice Management");
        Console.WriteLine("═══════════════════════════════════════");
        Console.ResetColor();
        
        DisplayCurrentInvoices();
        
        Console.WriteLine("\n📋 خيارات إدارة الفواتير:");
        Console.WriteLine("1. إضافة فاتورة جديدة");
        Console.WriteLine("2. تعديل فاتورة موجودة");
        Console.WriteLine("3. حذف فاتورة");
        Console.WriteLine("4. البحث في الفواتير");
    }
    
    static void ShowDiscountSystem()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Blue;
        Console.WriteLine("🎯 نظام الخصم الجديد - New Discount System");
        Console.WriteLine("═══════════════════════════════════════════════");
        Console.ResetColor();
        
        Console.WriteLine("✅ تم تحويل النظام بنجاح من نسبة مئوية إلى مبلغ ثابت");
        Console.WriteLine("✅ System successfully converted from percentage to fixed amount");
        
        DisplayDiscountSummary();
        
        Console.WriteLine("\n🎯 خيارات نظام الخصم:");
        Console.WriteLine("1. إضافة منتج بخصم");
        Console.WriteLine("2. تحديث خصم منتج");
        Console.WriteLine("3. عرض تقرير الخصومات");
        Console.WriteLine("4. اختبار النظام");
    }
    
    static void ShowReports()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Magenta;
        Console.WriteLine("📊 التقارير - Reports");
        Console.WriteLine("═══════════════════════");
        Console.ResetColor();
        
        Console.WriteLine("📈 التقارير المتاحة:");
        Console.WriteLine("1. تقرير الفواتير اليومي");
        Console.WriteLine("2. تقرير الخصومات");
        Console.WriteLine("3. تقرير المبيعات الشهري");
        Console.WriteLine("4. تقرير المخزون");
        Console.WriteLine("5. تقرير الأرباح والخسائر");
    }
    
    static void ShowSettings()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine("⚙️ الإعدادات - Settings");
        Console.WriteLine("═══════════════════════");
        Console.ResetColor();
        
        Console.WriteLine("🔧 إعدادات النظام:");
        Console.WriteLine("1. إعدادات قاعدة البيانات");
        Console.WriteLine("2. إعدادات الضرائب");
        Console.WriteLine("3. إعدادات الطباعة");
        Console.WriteLine("4. إعدادات المستخدمين");
        Console.WriteLine("5. إعدادات النسخ الاحتياطي");
    }
    
    static void ShowBackupRestore()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.DarkYellow;
        Console.WriteLine("💾 النسخ الاحتياطي - Backup & Restore");
        Console.WriteLine("═══════════════════════════════════════");
        Console.ResetColor();
        
        Console.WriteLine("💾 خيارات النسخ الاحتياطي:");
        Console.WriteLine("1. إنشاء نسخة احتياطية جديدة");
        Console.WriteLine("2. استعادة من نسخة احتياطية");
        Console.WriteLine("3. عرض النسخ الاحتياطية المتاحة");
        Console.WriteLine("4. حذف نسخة احتياطية قديمة");
        
        // عرض حالة النسخة الاحتياطية الحالية
        CheckBackupStatus();
    }
    
    static void DisplayCurrentInvoices()
    {
        try
        {
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    SELECT TOP 5
                        ii.Inv_ID,
                        ii.InvoiceNo,
                        ii.InvoiceDate,
                        ii.GrandTotal
                    FROM InvoiceInfo ii
                    ORDER BY ii.Inv_ID DESC";
                
                using (var command = new SqlCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine($"{"ID",-5} {"رقم الفاتورة",-15} {"التاريخ",-12} {"الإجمالي",-10}");
                    Console.WriteLine(new string('─', 45));
                    
                    while (reader.Read())
                    {
                        Console.WriteLine($"{reader[0],-5} {reader[1],-15} {reader[2],-12} {reader[3],-10}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في عرض الفواتير: {ex.Message}");
        }
    }
    
    static void DisplayDiscountSummary()
    {
        try
        {
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    SELECT 
                        COUNT(*) as TotalProducts,
                        SUM(Discount) as TotalDiscounts,
                        SUM(TotalAmount) as GrandTotal
                    FROM Invoice_Product";
                
                using (var command = new SqlCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        Console.WriteLine($"📦 إجمالي المنتجات: {reader[0]}");
                        Console.WriteLine($"💰 إجمالي الخصومات: {reader[1]} ريال");
                        Console.WriteLine($"💵 إجمالي المبيعات: {reader[2]} ريال");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في عرض ملخص الخصومات: {ex.Message}");
        }
    }
    
    static void CheckBackupStatus()
    {
        try
        {
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM sys.tables WHERE name = 'Invoice_Product_Backup'";
                
                using (var command = new SqlCommand(query, connection))
                {
                    int backupExists = (int)command.ExecuteScalar();
                    
                    if (backupExists > 0)
                    {
                        Console.WriteLine("✅ النسخة الاحتياطية متوفرة");
                        Console.WriteLine("✅ Backup available");
                    }
                    else
                    {
                        Console.WriteLine("⚠️ لا توجد نسخة احتياطية");
                        Console.WriteLine("⚠️ No backup available");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في فحص النسخة الاحتياطية: {ex.Message}");
        }
    }
}
