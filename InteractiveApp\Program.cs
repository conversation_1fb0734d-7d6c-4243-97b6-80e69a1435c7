using System;
using System.Data;
using Microsoft.Data.SqlClient;

class Program
{
    private static string connectionString = "Data Source=.\\SQLEXPRESS;Initial Catalog=INV_DB;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True";
    
    static void Main(string[] args)
    {
        Console.OutputEncoding = System.Text.Encoding.UTF8;
        Console.Clear();
        
        // عرض الشعار والترحيب
        DisplayWelcomeScreen();
        
        bool continueRunning = true;
        while (continueRunning)
        {
            try
            {
                DisplayMainMenu();
                string choice = Console.ReadLine();
                
                switch (choice)
                {
                    case "1":
                        DisplayCurrentInvoices();
                        break;
                    case "2":
                        AddNewProductWithDiscount();
                        break;
                    case "3":
                        UpdateProductDiscount();
                        break;
                    case "4":
                        CalculateInvoiceTotal();
                        break;
                    case "5":
                        CreateBackup();
                        break;
                    case "6":
                        ConvertAllDiscounts();
                        break;
                    case "7":
                        TestDiscountSystem();
                        break;
                    case "0":
                        continueRunning = false;
                        Console.WriteLine("🙏 شكراً لاستخدام مدير نظام الخصم!");
                        Console.WriteLine("🙏 Thank you for using Discount System Manager!");
                        break;
                    default:
                        Console.WriteLine("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى");
                        Console.WriteLine("❌ Invalid choice, please try again");
                        break;
                }
                
                if (continueRunning)
                {
                    Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                    Console.WriteLine("Press any key to continue...");
                    Console.ReadKey();
                    Console.Clear();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ: {ex.Message}");
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                Console.ReadKey();
                Console.Clear();
            }
        }
    }
    
    static void DisplayWelcomeScreen()
    {
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    مدير نظام الخصم                          ║");
        Console.WriteLine("║                Discount System Manager                       ║");
        Console.WriteLine("║                                                              ║");
        Console.WriteLine("║          تحويل من نسبة مئوية إلى مبلغ ثابت                  ║");
        Console.WriteLine("║        Convert from Percentage to Fixed Amount              ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();
    }
    
    static void DisplayMainMenu()
    {
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine("📋 القائمة الرئيسية - Main Menu");
        Console.WriteLine("═══════════════════════════════════");
        Console.ResetColor();
        
        Console.WriteLine("1️⃣  عرض الفواتير الحالية - Display Current Invoices");
        Console.WriteLine("2️⃣  إضافة منتج بخصم - Add Product with Discount");
        Console.WriteLine("3️⃣  تحديث خصم منتج - Update Product Discount");
        Console.WriteLine("4️⃣  حساب إجمالي الفاتورة - Calculate Invoice Total");
        Console.WriteLine("5️⃣  إنشاء نسخة احتياطية - Create Backup");
        Console.WriteLine("6️⃣  تحويل جميع الخصومات - Convert All Discounts");
        Console.WriteLine("7️⃣  اختبار النظام - Test System");
        Console.WriteLine("0️⃣  خروج - Exit");
        Console.WriteLine();
        Console.Write("اختر رقماً من القائمة - Choose a number: ");
    }
    
    static void DisplayCurrentInvoices()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Green;
        Console.WriteLine("📊 الفواتير الحالية - Current Invoices");
        Console.WriteLine("═══════════════════════════════════════");
        Console.ResetColor();
        
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            string query = @"
                SELECT 
                    ip.IPo_ID as 'رقم المنتج',
                    ii.InvoiceNo as 'رقم الفاتورة',
                    ip.SellingPrice as 'سعر البيع',
                    ip.Qty as 'الكمية',
                    ip.Amount as 'المبلغ الأساسي',
                    ip.DiscountPer as 'خصم نسبة مئوية',
                    ip.Discount as 'خصم مبلغ',
                    ip.VAT as 'ضريبة القيمة المضافة',
                    ip.TotalAmount as 'المجموع الإجمالي'
                FROM Invoice_Product ip
                LEFT JOIN InvoiceInfo ii ON ip.InvoiceID = ii.Inv_ID
                ORDER BY ip.IPo_ID";
            
            using (var command = new SqlCommand(query, connection))
            using (var reader = command.ExecuteReader())
            {
                Console.WriteLine($"{"ID",-5} {"Invoice",-10} {"Price",-10} {"Qty",-5} {"Amount",-10} {"Disc%",-8} {"DiscAmt",-10} {"VAT",-8} {"Total",-10}");
                Console.WriteLine(new string('─', 80));
                
                decimal grandTotal = 0;
                int count = 0;
                
                while (reader.Read())
                {
                    Console.WriteLine($"{reader[0],-5} {reader[1],-10} {reader[2],-10} {reader[3],-5} {reader[4],-10} {reader[5],-8} {reader[6],-10} {reader[7],-8} {reader[8],-10}");
                    grandTotal += Convert.ToDecimal(reader[8]);
                    count++;
                }
                
                Console.WriteLine(new string('─', 80));
                Console.ForegroundColor = ConsoleColor.Cyan;
                Console.WriteLine($"📊 إجمالي عدد المنتجات: {count}");
                Console.WriteLine($"💰 إجمالي المبلغ: {grandTotal:F2} ريال");
                Console.ResetColor();
            }
        }
    }
    
    static void AddNewProductWithDiscount()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Blue;
        Console.WriteLine("➕ إضافة منتج جديد بخصم - Add New Product with Discount");
        Console.WriteLine("═══════════════════════════════════════════════════════════");
        Console.ResetColor();
        
        try
        {
            Console.Write("رقم الفاتورة - Invoice ID [1]: ");
            string invoiceInput = Console.ReadLine();
            int invoiceId = string.IsNullOrEmpty(invoiceInput) ? 1 : int.Parse(invoiceInput);
            
            Console.Write("رقم المنتج - Product ID [5]: ");
            string productInput = Console.ReadLine();
            int productId = string.IsNullOrEmpty(productInput) ? 5 : int.Parse(productInput);
            
            Console.Write("سعر التكلفة - Cost Price [300]: ");
            string costInput = Console.ReadLine();
            decimal costPrice = string.IsNullOrEmpty(costInput) ? 300 : decimal.Parse(costInput);
            
            Console.Write("سعر البيع - Selling Price [450]: ");
            string sellingInput = Console.ReadLine();
            decimal sellingPrice = string.IsNullOrEmpty(sellingInput) ? 450 : decimal.Parse(sellingInput);
            
            Console.Write("الكمية - Quantity [1]: ");
            string qtyInput = Console.ReadLine();
            int qty = string.IsNullOrEmpty(qtyInput) ? 1 : int.Parse(qtyInput);
            
            Console.Write("مبلغ الخصم - Discount Amount [50]: ");
            string discountInput = Console.ReadLine();
            decimal discountAmount = string.IsNullOrEmpty(discountInput) ? 50 : decimal.Parse(discountInput);
            
            Console.Write("نسبة ضريبة القيمة المضافة - VAT Percentage [15]: ");
            string vatInput = Console.ReadLine();
            decimal vatPer = string.IsNullOrEmpty(vatInput) ? 15 : decimal.Parse(vatInput);
            
            Console.Write("الباركود - Barcode [TEST004]: ");
            string barcode = Console.ReadLine();
            if (string.IsNullOrEmpty(barcode)) barcode = "TEST004";
            
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (var command = new SqlCommand("sp_AddInvoiceProduct_WithFixedDiscount", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@InvoiceID", invoiceId);
                    command.Parameters.AddWithValue("@ProductID", productId);
                    command.Parameters.AddWithValue("@CostPrice", costPrice);
                    command.Parameters.AddWithValue("@SellingPrice", sellingPrice);
                    command.Parameters.AddWithValue("@Qty", qty);
                    command.Parameters.AddWithValue("@DiscountAmount", discountAmount);
                    command.Parameters.AddWithValue("@VATPer", vatPer);
                    command.Parameters.AddWithValue("@Barcode", barcode);
                    
                    command.ExecuteNonQuery();
                    
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine("✅ تم إضافة المنتج بنجاح!");
                    Console.WriteLine($"✅ Product added: Price {sellingPrice}, Discount {discountAmount}, VAT {vatPer}%");
                    Console.WriteLine($"✅ Final Amount: {(sellingPrice * qty) - discountAmount + ((sellingPrice * qty - discountAmount) * vatPer / 100):F2}");
                    Console.ResetColor();
                }
            }
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"❌ خطأ في إضافة المنتج: {ex.Message}");
            Console.ResetColor();
        }
    }
    
    static void UpdateProductDiscount()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Magenta;
        Console.WriteLine("🔄 تحديث خصم منتج - Update Product Discount");
        Console.WriteLine("═══════════════════════════════════════════════");
        Console.ResetColor();
        
        // عرض المنتجات الحالية أولاً
        DisplayCurrentInvoices();
        
        try
        {
            Console.Write("\nرقم المنتج المراد تحديثه - Product ID to update: ");
            int ipoId = int.Parse(Console.ReadLine());
            
            Console.Write("مبلغ الخصم الجديد - New Discount Amount: ");
            decimal newDiscountAmount = decimal.Parse(Console.ReadLine());
            
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (var command = new SqlCommand("sp_UpdateProductDiscount", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@IPo_ID", ipoId);
                    command.Parameters.AddWithValue("@NewDiscountAmount", newDiscountAmount);
                    
                    command.ExecuteNonQuery();
                    
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine("✅ تم تحديث الخصم بنجاح!");
                    Console.WriteLine("✅ Discount updated successfully!");
                    Console.ResetColor();
                }
            }
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"❌ خطأ في تحديث الخصم: {ex.Message}");
            Console.ResetColor();
        }
    }
    
    static void CalculateInvoiceTotal()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine("💰 حساب إجمالي الفاتورة - Calculate Invoice Total");
        Console.WriteLine("═══════════════════════════════════════════════════");
        Console.ResetColor();
        
        try
        {
            Console.Write("رقم الفاتورة - Invoice ID [1]: ");
            string input = Console.ReadLine();
            int invoiceId = string.IsNullOrEmpty(input) ? 1 : int.Parse(input);
            
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (var command = new SqlCommand("sp_CalculateInvoiceTotal", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@InvoiceID", invoiceId);
                    var outputParam = new SqlParameter("@GrandTotal", SqlDbType.Decimal) { Direction = ParameterDirection.Output };
                    command.Parameters.Add(outputParam);
                    
                    command.ExecuteNonQuery();
                    
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine($"💰 إجمالي الفاتورة رقم {invoiceId}: {outputParam.Value} ريال");
                    Console.WriteLine($"💰 Invoice {invoiceId} Total: {outputParam.Value} SAR");
                    Console.ResetColor();
                }
            }
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"❌ خطأ في حساب الإجمالي: {ex.Message}");
            Console.ResetColor();
        }
    }
    
    static void CreateBackup()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.DarkYellow;
        Console.WriteLine("💾 إنشاء نسخة احتياطية - Create Backup");
        Console.WriteLine("═══════════════════════════════════════════");
        Console.ResetColor();
        
        try
        {
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                string backupQuery = @"
                    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Invoice_Product_Backup')
                    BEGIN
                        SELECT * INTO Invoice_Product_Backup FROM Invoice_Product
                        PRINT 'تم إنشاء النسخة الاحتياطية'
                    END
                    ELSE
                    BEGIN
                        PRINT 'النسخة الاحتياطية موجودة مسبقاً'
                    END";
                
                using (var command = new SqlCommand(backupQuery, connection))
                {
                    command.ExecuteNonQuery();
                    
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine("✅ تم التحقق من النسخة الاحتياطية بنجاح!");
                    Console.WriteLine("✅ Backup verified successfully!");
                    Console.ResetColor();
                }
            }
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"❌ خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
            Console.ResetColor();
        }
    }
    
    static void ConvertAllDiscounts()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Red;
        Console.WriteLine("⚠️  تحويل جميع الخصومات - Convert All Discounts");
        Console.WriteLine("═══════════════════════════════════════════════════");
        Console.WriteLine("⚠️  تحذير: هذا الإجراء سيحول جميع الخصومات من نسبة مئوية إلى مبلغ ثابت");
        Console.WriteLine("⚠️  Warning: This will convert all discounts from percentage to fixed amount");
        Console.ResetColor();
        
        Console.Write("هل أنت متأكد؟ (y/n): ");
        string confirmation = Console.ReadLine();
        
        if (confirmation?.ToLower() == "y" || confirmation?.ToLower() == "yes")
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    string convertQuery = @"
                        UPDATE Invoice_Product 
                        SET 
                            Discount = CASE 
                                WHEN DiscountPer > 0 THEN (Amount * DiscountPer / 100)
                                ELSE Discount
                            END,
                            DiscountPer = 0
                        WHERE DiscountPer > 0;
                        
                        UPDATE Invoice_Product 
                        SET TotalAmount = Amount - Discount + VAT;";
                    
                    using (var command = new SqlCommand(convertQuery, connection))
                    {
                        int rowsAffected = command.ExecuteNonQuery();
                        
                        Console.ForegroundColor = ConsoleColor.Green;
                        Console.WriteLine($"✅ تم تحويل النظام بنجاح!");
                        Console.WriteLine($"✅ System converted successfully!");
                        Console.ResetColor();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ خطأ في التحويل: {ex.Message}");
                Console.ResetColor();
            }
        }
        else
        {
            Console.WriteLine("تم إلغاء العملية - Operation cancelled");
        }
    }
    
    static void TestDiscountSystem()
    {
        Console.Clear();
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.WriteLine("🧪 اختبار النظام - Test System");
        Console.WriteLine("═══════════════════════════════════");
        Console.ResetColor();
        
        try
        {
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (var command = new SqlCommand("sp_TestDiscountSystem", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    
                    using (var reader = command.ExecuteReader())
                    {
                        Console.WriteLine($"{"Status",-20} {"ID",-5} {"Price",-10} {"Qty",-5} {"Amount",-10} {"Disc%",-8} {"DiscAmt",-10} {"Total",-10}");
                        Console.WriteLine(new string('─', 80));
                        
                        while (reader.Read())
                        {
                            Console.WriteLine($"{reader[0],-20} {reader[1],-5} {reader[2],-10} {reader[3],-5} {reader[4],-10} {reader[5],-8} {reader[6],-10} {reader[7],-10}");
                        }
                    }
                }
                
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("✅ تم اختبار النظام بنجاح!");
                Console.WriteLine("✅ System tested successfully!");
                Console.ResetColor();
            }
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
            Console.ResetColor();
        }
    }
}
