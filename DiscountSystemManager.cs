using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Windows.Forms;

namespace DiscountSystemManager
{
    /// <summary>
    /// مدير نظام الخصم - تحويل من نسبة مئوية إلى مبلغ ثابت
    /// Discount System Manager - Convert from percentage to fixed amount
    /// </summary>
    public partial class DiscountManagerForm : Form
    {
        private string connectionString = "Data Source=.\\SQLEXPRESS;Initial Catalog=INV_DB;Integrated Security=True;MultipleActiveResultSets=True";

        public DiscountManagerForm()
        {
            InitializeComponent();
            LoadInvoiceData();
        }

        /// <summary>
        /// تحميل بيانات الفواتير
        /// Load invoice data
        /// </summary>
        private void LoadInvoiceData()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    string query = @"
                        SELECT
                            ip.IPo_ID,
                            ii.InvoiceNo as 'رقم_الفاتورة',
                            p.ProductName as 'اسم_المنتج',
                            ip.SellingPrice as 'سعر_البيع',
                            ip.Qty as 'الكمية',
                            ip.Amount as 'المبلغ_الأساسي',
                            ip.DiscountPer as 'خصم_نسبة_مئوية',
                            ip.Discount as 'خصم_مبلغ',
                            ip.TotalAmount as 'المجموع_الإجمالي'
                        FROM Invoice_Product ip
                        LEFT JOIN InvoiceInfo ii ON ip.InvoiceID = ii.Inv_ID
                        LEFT JOIN Product p ON ip.ProductID = p.ProductID
                        ORDER BY ip.IPo_ID";

                    SqlDataAdapter adapter = new SqlDataAdapter(query, conn);
                    DataTable dt = new DataTable();
                    adapter.Fill(dt);

                    dataGridViewInvoices.DataSource = dt;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث خصم منتج معين
        /// Update discount for specific product
        /// </summary>
        private void UpdateProductDiscount(int ipoId, decimal discountAmount)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("sp_UpdateProductDiscount", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IPo_ID", ipoId);
                        cmd.Parameters.AddWithValue("@NewDiscountAmount", discountAmount);

                        cmd.ExecuteNonQuery();
                        MessageBox.Show("تم تحديث الخصم بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadInvoiceData(); // إعادة تحميل البيانات
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الخصم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحويل جميع الخصومات من نسبة مئوية إلى مبلغ ثابت
        /// Convert all discounts from percentage to fixed amount
        /// </summary>
        private void ConvertAllDiscounts()
        {
            try
            {
                DialogResult result = MessageBox.Show(
                    "هل أنت متأكد من تحويل جميع الخصومات من نسبة مئوية إلى مبلغ ثابت؟\nهذا الإجراء لا يمكن التراجع عنه!",
                    "تأكيد التحويل",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    using (SqlConnection conn = new SqlConnection(connectionString))
                    {
                        conn.Open();

                        // تنفيذ سكريبت التحديث
                        string updateScript = @"
                            -- تحديث البيانات الموجودة
                            UPDATE Invoice_Product
                            SET
                                Discount = CASE
                                    WHEN DiscountPer > 0 THEN (Amount * DiscountPer / 100)
                                    ELSE Discount
                                END,
                                DiscountPer = 0
                            WHERE DiscountPer > 0;

                            -- إعادة حساب المجموع الإجمالي
                            UPDATE Invoice_Product
                            SET TotalAmount = Amount - Discount + VAT;";

                        using (SqlCommand cmd = new SqlCommand(updateScript, conn))
                        {
                            int rowsAffected = cmd.ExecuteNonQuery();
                            MessageBox.Show($"تم تحويل النظام بنجاح!\nتم تحديث {rowsAffected} سجل.", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadInvoiceData();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحويل النظام: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من البيانات
        /// Create backup of data
        /// </summary>
        private void CreateBackup()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    string backupQuery = @"
                        IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Invoice_Product_Backup')
                        BEGIN
                            SELECT * INTO Invoice_Product_Backup FROM Invoice_Product
                        END";

                    using (SqlCommand cmd = new SqlCommand(backupQuery, conn))
                    {
                        cmd.ExecuteNonQuery();
                        MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // أحداث الأزرار - Button Events
        private void btnConvertAll_Click(object sender, EventArgs e)
        {
            ConvertAllDiscounts();
        }

        private void btnCreateBackup_Click(object sender, EventArgs e)
        {
            CreateBackup();
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadInvoiceData();
        }

        private void btnUpdateDiscount_Click(object sender, EventArgs e)
        {
            if (dataGridViewInvoices.SelectedRows.Count > 0)
            {
                int ipoId = Convert.ToInt32(dataGridViewInvoices.SelectedRows[0].Cells["IPo_ID"].Value);

                string input = Microsoft.VisualBasic.Interaction.InputBox(
                    "أدخل مبلغ الخصم الجديد:",
                    "تحديث الخصم",
                    "0");

                if (decimal.TryParse(input, out decimal discountAmount))
                {
                    UpdateProductDiscount(ipoId, discountAmount);
                }
                else
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح!", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار سجل للتحديث!", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
}
