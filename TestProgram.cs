using System;
using System.Data;
using Microsoft.Data.SqlClient;

class TestProgram
{
    private static string connectionString = "Data Source=.\\SQLEXPRESS;Initial Catalog=INV_DB;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True";
    
    static void Main(string[] args)
    {
        Console.OutputEncoding = System.Text.Encoding.UTF8;
        Console.WriteLine("🎯 مرحباً بك في اختبار نظام الخصم الجديد");
        Console.WriteLine("🎯 Welcome to New Discount System Test");
        Console.WriteLine("=" + new string('=', 50));
        
        try
        {
            // اختبار الاتصال بقاعدة البيانات
            TestDatabaseConnection();
            
            // عرض البيانات الحالية
            DisplayCurrentData();
            
            // اختبار إضافة منتج جديد بخصم
            TestAddProductWithDiscount();
            
            // عرض النتائج النهائية
            DisplayFinalResults();
            
            Console.WriteLine("\n✅ تم الاختبار بنجاح!");
            Console.WriteLine("✅ Test completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ: {ex.Message}");
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
        
        Console.WriteLine("\nاضغط أي مفتاح للخروج...");
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
    
    static void TestDatabaseConnection()
    {
        Console.WriteLine("\n🔗 اختبار الاتصال بقاعدة البيانات...");
        Console.WriteLine("🔗 Testing database connection...");
        
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            Console.WriteLine("✅ تم الاتصال بقاعدة البيانات بنجاح");
            Console.WriteLine("✅ Database connection successful");
        }
    }
    
    static void DisplayCurrentData()
    {
        Console.WriteLine("\n📊 البيانات الحالية في النظام:");
        Console.WriteLine("📊 Current data in system:");
        
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            string query = @"
                SELECT 
                    IPo_ID,
                    SellingPrice,
                    Qty,
                    Amount,
                    DiscountPer,
                    Discount,
                    TotalAmount
                FROM Invoice_Product 
                ORDER BY IPo_ID";
            
            using (var command = new SqlCommand(query, connection))
            using (var reader = command.ExecuteReader())
            {
                Console.WriteLine($"{"ID",-5} {"Price",-10} {"Qty",-5} {"Amount",-10} {"Disc%",-8} {"DiscAmt",-10} {"Total",-10}");
                Console.WriteLine(new string('-', 60));
                
                while (reader.Read())
                {
                    Console.WriteLine($"{reader[0],-5} {reader[1],-10} {reader[2],-5} {reader[3],-10} {reader[4],-8} {reader[5],-10} {reader[6],-10}");
                }
            }
        }
    }
    
    static void TestAddProductWithDiscount()
    {
        Console.WriteLine("\n➕ اختبار إضافة منتج جديد بخصم 30 ريال...");
        Console.WriteLine("➕ Testing add new product with 30 SAR discount...");
        
        try
        {
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (var command = new SqlCommand("sp_AddInvoiceProduct_WithFixedDiscount", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@InvoiceID", 1);
                    command.Parameters.AddWithValue("@ProductID", 5);
                    command.Parameters.AddWithValue("@CostPrice", 200.000);
                    command.Parameters.AddWithValue("@SellingPrice", 350.000);
                    command.Parameters.AddWithValue("@Qty", 1);
                    command.Parameters.AddWithValue("@DiscountAmount", 30.000);
                    command.Parameters.AddWithValue("@VATPer", 15.000);
                    command.Parameters.AddWithValue("@Barcode", "TEST002");
                    
                    command.ExecuteNonQuery();
                    Console.WriteLine("✅ تم إضافة المنتج بنجاح مع خصم 30 ريال");
                    Console.WriteLine("✅ Product added successfully with 30 SAR discount");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ تحذير: {ex.Message}");
            Console.WriteLine("⚠️ سيتم المتابعة مع البيانات الموجودة");
        }
    }
    
    static void DisplayFinalResults()
    {
        Console.WriteLine("\n📈 النتائج النهائية:");
        Console.WriteLine("📈 Final results:");
        
        using (var connection = new SqlConnection(connectionString))
        {
            connection.Open();
            
            // عرض جميع البيانات
            string query = @"
                SELECT 
                    IPo_ID,
                    SellingPrice,
                    Qty,
                    Amount,
                    Discount,
                    VAT,
                    TotalAmount
                FROM Invoice_Product 
                ORDER BY IPo_ID";
            
            using (var command = new SqlCommand(query, connection))
            using (var reader = command.ExecuteReader())
            {
                Console.WriteLine($"{"ID",-5} {"Price",-10} {"Qty",-5} {"Amount",-10} {"Discount",-10} {"VAT",-8} {"Total",-10}");
                Console.WriteLine(new string('-', 65));
                
                decimal grandTotal = 0;
                while (reader.Read())
                {
                    Console.WriteLine($"{reader[0],-5} {reader[1],-10} {reader[2],-5} {reader[3],-10} {reader[4],-10} {reader[5],-8} {reader[6],-10}");
                    grandTotal += Convert.ToDecimal(reader[6]);
                }
                
                Console.WriteLine(new string('-', 65));
                Console.WriteLine($"💰 إجمالي الفاتورة: {grandTotal} ريال");
                Console.WriteLine($"💰 Invoice Total: {grandTotal} SAR");
            }
        }
    }
}
