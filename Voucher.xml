<?xml version="1.0" standalone="yes"?>
<xs:schema id="NewDataSet" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="NewDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Table1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" type="xs:int" minOccurs="0" />
              <xs:element name="VoucherNo" type="xs:string" minOccurs="0" />
              <xs:element name="Name" type="xs:string" minOccurs="0" />
              <xs:element name="Date" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Details" type="xs:string" minOccurs="0" />
              <xs:element name="GrandTotal" type="xs:decimal" minOccurs="0" />
              <xs:element name="VD_ID" type="xs:int" minOccurs="0" />
              <xs:element name="VoucherID" type="xs:int" minOccurs="0" />
              <xs:element name="Particulars" type="xs:string" minOccurs="0" />
              <xs:element name="Amount" type="xs:decimal" minOccurs="0" />
              <xs:element name="Note" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>